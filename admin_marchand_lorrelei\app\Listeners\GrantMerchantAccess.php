<?php

namespace App\Listeners;

use App\Events\MerchantApproved;
use App\Models\MarchandAbonnement;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class GrantMerchantAccess implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MerchantApproved $event): void
    {
        try {
            $validation = $event->validation;
            $marchand = $event->marchand;

            if (!$marchand) {
                Log::warning('Marchand non trouvé pour accorder l\'accès', [
                    'validation_id' => $validation->id,
                ]);
                return;
            }

            // Créer un abonnement gratuit par défaut si pas déjà existant
            $existingSubscription = MarchandAbonnement::where('marchand_id', $marchand->id)
                ->where('statut', 'actif')
                ->first();

            if (!$existingSubscription) {
                MarchandAbonnement::creerAbonnement($marchand->id, 'gratuit');
                
                Log::info('Abonnement gratuit créé pour le marchand approuvé', [
                    'validation_id' => $validation->id,
                    'marchand_id' => $marchand->id,
                ]);
            }

            // Mettre à jour le statut du marchand pour s'assurer qu'il est actif
            $marchand->update([
                'statut_validation' => 'valide',
                'date_validation' => now(),
            ]);

            // Mettre à jour l'utilisateur pour s'assurer qu'il a le bon rôle
            $validation->user->update([
                'role' => 'Marchand',
                'is_active' => true,
            ]);

            Log::info('Accès marchand accordé avec succès', [
                'validation_id' => $validation->id,
                'user_id' => $validation->user_id,
                'marchand_id' => $marchand->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'octroi d\'accès marchand', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(MerchantApproved $event, \Throwable $exception): void
    {
        Log::error('Échec définitif de l\'octroi d\'accès marchand', [
            'validation_id' => $event->validation->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
