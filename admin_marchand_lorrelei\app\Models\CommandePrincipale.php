<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CommandePrincipale extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'commandes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'numero_commande',
        'montant_total_ht',
        'montant_total_ttc',
        'statut_global',
        'date_commande',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'montant_total_ht' => 'decimal:2',
        'montant_total_ttc' => 'decimal:2',
        'date_commande' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Client qui a passé la commande
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Conversations liées à cette commande
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(ClientMarchandConversation::class);
    }
}
