import React from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    BookOpen,
    CheckCircle,
    Users,
    ShoppingBag,
    CreditCard,
    Truck,
    BarChart3,
    MessageSquare,
    Shield,
    Star,
    ArrowRight,
    Download,
    ExternalLink
} from 'lucide-react';

interface GuideSection {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
    steps: string[];
    tips?: string[];
}

const guideSections: GuideSection[] = [
    {
        id: 'inscription',
        title: 'Inscription et Validation',
        description: 'Comment créer votre compte marchand et être approuvé',
        icon: <CheckCircle className="h-6 w-6" />,
        steps: [
            'Créez votre compte sur la plateforme marchand',
            'Remplissez vos informations personnelles',
            'Ajoutez vos informations de facturation',
            'Configurez votre boutique',
            'Soumettez vos documents requis',
            'Attendez la validation (24-48h)',
            'Recevez votre confirmation par email'
        ],
        tips: [
            'Préparez tous vos documents à l\'avance',
            'Assurez-vous que vos informations sont exactes',
            'Répondez rapidement aux demandes de clarification'
        ]
    },
    {
        id: 'boutique',
        title: 'Configuration de votre Boutique',
        description: 'Personnalisez votre boutique pour attirer les clients',
        icon: <ShoppingBag className="h-6 w-6" />,
        steps: [
            'Accédez à votre dashboard marchand',
            'Complétez votre profil boutique',
            'Ajoutez votre logo et bannière',
            'Rédigez une description attractive',
            'Configurez vos catégories de produits',
            'Définissez vos politiques de retour',
            'Activez votre boutique'
        ],
        tips: [
            'Utilisez des images de haute qualité',
            'Rédigez une description claire et engageante',
            'Mettez en avant vos spécialités'
        ]
    },
    {
        id: 'produits',
        title: 'Gestion des Produits',
        description: 'Ajoutez et gérez efficacement vos produits',
        icon: <BookOpen className="h-6 w-6" />,
        steps: [
            'Créez vos premières fiches produits',
            'Ajoutez des photos de qualité',
            'Rédigez des descriptions détaillées',
            'Définissez vos prix et stocks',
            'Configurez les variantes (taille, couleur)',
            'Organisez par catégories',
            'Activez vos produits'
        ],
        tips: [
            'Utilisez des mots-clés pertinents',
            'Mettez à jour régulièrement vos stocks',
            'Proposez plusieurs angles de vue'
        ]
    },
    {
        id: 'commandes',
        title: 'Traitement des Commandes',
        description: 'Gérez efficacement vos commandes et livraisons',
        icon: <Truck className="h-6 w-6" />,
        steps: [
            'Recevez les notifications de nouvelles commandes',
            'Confirmez la disponibilité des produits',
            'Préparez les commandes rapidement',
            'Configurez vos zones de livraison',
            'Choisissez vos transporteurs',
            'Suivez les livraisons',
            'Confirmez la réception'
        ],
        tips: [
            'Traitez les commandes dans les 24h',
            'Communiquez proactivement avec les clients',
            'Emballez soigneusement vos produits'
        ]
    },
    {
        id: 'paiements',
        title: 'Gestion des Paiements',
        description: 'Configurez vos méthodes de paiement et versements',
        icon: <CreditCard className="h-6 w-6" />,
        steps: [
            'Configurez vos comptes bancaires',
            'Choisissez vos méthodes de paiement',
            'Définissez vos conditions de versement',
            'Suivez vos revenus en temps réel',
            'Gérez vos factures et reçus',
            'Consultez vos rapports financiers'
        ],
        tips: [
            'Vérifiez régulièrement vos versements',
            'Gardez vos informations bancaires à jour',
            'Consultez vos rapports mensuels'
        ]
    },
    {
        id: 'communication',
        title: 'Communication Client',
        description: 'Maintenez une excellente relation client',
        icon: <MessageSquare className="h-6 w-6" />,
        steps: [
            'Répondez rapidement aux messages',
            'Utilisez le chat intégré',
            'Gérez les avis clients',
            'Résolvez les litiges à l\'amiable',
            'Demandez des avis après livraison',
            'Fidélisez vos clients'
        ],
        tips: [
            'Répondez dans les 2h en moyenne',
            'Soyez professionnel et courtois',
            'Anticipez les questions fréquentes'
        ]
    }
];

const resources = [
    {
        title: 'Templates de Produits',
        description: 'Modèles Excel pour importer vos produits en masse',
        icon: <Download className="h-4 w-4" />,
        action: 'Télécharger',
        href: '#'
    },
    {
        title: 'Guide des Photos',
        description: 'Conseils pour prendre de belles photos de produits',
        icon: <ExternalLink className="h-4 w-4" />,
        action: 'Consulter',
        href: '#'
    },
    {
        title: 'Politique de Qualité',
        description: 'Standards de qualité requis sur Lorrelei',
        icon: <Shield className="h-4 w-4" />,
        action: 'Lire',
        href: '#'
    }
];

export default function GuideMarchand() {
    return (
        <AppLayout>
            <Head title="Guide du Marchand - Lorrelei" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <div className="flex justify-center mb-6">
                            <div className="bg-green-100 p-4 rounded-full">
                                <BookOpen className="h-12 w-12 text-green-600" />
                            </div>
                        </div>
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Guide du Marchand Lorrelei
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            Tout ce que vous devez savoir pour réussir en tant que marchand sur notre plateforme.
                            De l'inscription à la gestion quotidienne, suivez notre guide étape par étape.
                        </p>
                        <div className="flex justify-center mt-6">
                            <Badge variant="secondary" className="text-sm">
                                <Star className="h-4 w-4 mr-1" />
                                Guide Complet 2024
                            </Badge>
                        </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">500+</div>
                                <div className="text-sm text-gray-600">Marchands Actifs</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6 text-center">
                                <ShoppingBag className="h-8 w-8 text-green-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">10K+</div>
                                <div className="text-sm text-gray-600">Produits Vendus</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6 text-center">
                                <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">95%</div>
                                <div className="text-sm text-gray-600">Satisfaction Client</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6 text-center">
                                <CreditCard className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">24h</div>
                                <div className="text-sm text-gray-600">Délai de Paiement</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Guide Sections */}
                    <div className="space-y-8 mb-12">
                        {guideSections.map((section, index) => (
                            <Card key={section.id} className="overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-green-500 to-blue-500 text-white">
                                    <div className="flex items-center space-x-4">
                                        <div className="bg-white/20 p-2 rounded-lg">
                                            {section.icon}
                                        </div>
                                        <div>
                                            <CardTitle className="text-xl">
                                                {index + 1}. {section.title}
                                            </CardTitle>
                                            <CardDescription className="text-green-100">
                                                {section.description}
                                            </CardDescription>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-3">Étapes à suivre :</h4>
                                            <ul className="space-y-2">
                                                {section.steps.map((step, stepIndex) => (
                                                    <li key={stepIndex} className="flex items-start space-x-3">
                                                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                                                        <span className="text-gray-700">{step}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                        {section.tips && (
                                            <div>
                                                <h4 className="font-semibold text-gray-900 mb-3">Conseils pratiques :</h4>
                                                <ul className="space-y-2">
                                                    {section.tips.map((tip, tipIndex) => (
                                                        <li key={tipIndex} className="flex items-start space-x-3">
                                                            <Star className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                                                            <span className="text-gray-700">{tip}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Resources */}
                    <Card className="mb-12">
                        <CardHeader>
                            <CardTitle className="text-2xl">Ressources Utiles</CardTitle>
                            <CardDescription>
                                Téléchargez nos outils et consultez nos guides complémentaires
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-3 gap-6">
                                {resources.map((resource, index) => (
                                    <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex items-start space-x-3">
                                            <div className="bg-blue-100 p-2 rounded">
                                                {resource.icon}
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="font-semibold text-gray-900">{resource.title}</h4>
                                                <p className="text-sm text-gray-600 mb-3">{resource.description}</p>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    {resource.action}
                                                    <ArrowRight className="h-4 w-4 ml-2" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* CTA */}
                    <Card className="bg-gradient-to-r from-green-500 to-blue-500 text-white">
                        <CardContent className="p-8 text-center">
                            <h3 className="text-2xl font-bold mb-4">Prêt à commencer ?</h3>
                            <p className="text-green-100 mb-6 max-w-2xl mx-auto">
                                Rejoignez notre communauté de marchands prospères et commencez à vendre dès aujourd'hui.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button variant="secondary" size="lg">
                                    Créer mon compte marchand
                                </Button>
                                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-green-600">
                                    Contacter le support
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
