<?php

namespace App\Filament\Widgets;

use App\Models\CommandePrincipale;
use App\Services\DashboardStatsService;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Collection;

class GlobalOrdersWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 3;

    protected static ?string $heading = 'Commandes Globales Récentes';

    public function table(Table $table): Table
    {
        return $table->query(
            CommandePrincipale::query()->where('id', null)
        )
            ->columns([
                Tables\Columns\TextColumn::make('numero_commande')
                    ->label('N° Commande')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->weight('bold')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('client_nom')
                    ->label('Client')
                    ->searchable()
                    ->icon('heroicon-m-user')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('client_email')
                    ->label('Email')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('nombre_marchands')
                    ->label('Marchands')
                    ->badge()
                    ->color('info')
                    ->formatStateUsing(fn($state): string => $state . ' marchand' . ($state > 1 ? 's' : '')),

                Tables\Columns\TextColumn::make('montant_total_ttc')
                    ->label('Montant Total TTC')
                    ->money('XOF')
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('montant_commission_plateforme')
                    ->label('Commission Plateforme')
                    ->money('XOF')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('statut_global')
                    ->label('Statut')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'EnAttente' => 'gray',
                        'PayementConfirme' => 'warning',
                        'EnTraitement' => 'primary',
                        'PartielExpedié' => 'info',
                        'TotalementExpedié' => 'success',
                        'Livré' => 'success',
                        'Annulé' => 'danger',
                        'PartielLivré' => 'success',
                        'TotalementLivré' => 'success',
                        'Terminé' => 'success',
                        'Rembousé' => 'danger',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'EnAttente' => 'En attente',
                        'PayementConfirme' => 'Payement confirmé',
                        'EnTraitement' => 'En traitement',
                        'PartielExpedié' => 'Partiellement expédié',
                        'TotalementExpedié' => 'Totalement expédié',
                        'PartielLivré' => 'Partiellement livré',
                        'TotalementLivré' => 'Totalement livré',
                        'Terminé' => 'Terminé',
                        'Rembousé' => 'Rembousé',
                        default => ucfirst(str_replace('_', ' ', $state)),
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date de création')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_livraison_souhaitee')
                    ->label('Livraison souhaitée')
                    ->date('d/m/Y')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut_global')
                    ->label('Statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'PayementConfirme' => 'Payement confirmé',
                        'EnTraitement' => 'En traitement',
                        'PartielExpedié' => 'Partiellement expédié',
                        'TotalementExpedié' => 'Totalement expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'PartielLivré' => 'Partiellement livré',
                        'TotalementLivré' => 'Totalement livré',
                        'Terminé' => 'Terminé',
                        'Rembousé' => 'Rembousé',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        \Filament\Forms\Components\DatePicker::make('created_from')
                            ->label('Du'),
                        \Filament\Forms\Components\DatePicker::make('created_until')
                            ->label('Au'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn($query, $date) => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn($query, $date) => $query->whereDate('created_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('Voir Détails')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->url(function ($record): string {
                        // Redirection vers le dashboard admin des commandes
                        return route('admin.dashboard.orders.show', ['order' => $record->id]);
                    })
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('view_suborders')
                    ->label('Sous-commandes')
                    ->icon('heroicon-m-squares-2x2')
                    ->color('info')
                    ->url(function ($record): string {
                        return route('admin.dashboard.orders.suborders', ['order' => $record->id]);
                    })
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('export')
                        ->label('Exporter')
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function () {
                            // TODO: Implémenter la logique d'export
                            return redirect()->back()->with('success', 'Export en cours de développement');
                        }),
                ]),
            ])
            ->emptyStateHeading('Aucune commande')
            ->emptyStateDescription('Les commandes apparaîtront ici.')
            ->emptyStateIcon('heroicon-o-shopping-cart')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([20, 50, 100]);
    }

    public function getTableRecords(): Collection
    {
        $dashboardService = new DashboardStatsService();
        $commandes = $dashboardService->getCommandesGlobales(now()->startOfMonth(), now()->endOfMonth());
        $commandesCollection = new Collection();
        foreach ($commandes as $data) {
            $commande = CommandePrincipale::find($data->id);
            if ($commande) {
                $commande->nombre_marchands = $data->nombre_marchands;
                $commande->montant_commission_plateforme = $data->montant_commission_plateforme;
                $commande->montant_total_ttc = $data->montant_total_ttc;
                $commande->montant_total_ht = $data->montant_total_ht;
                $commande->statut_global = $data->statut_global;
                $commande->created_at = $data->created_at;
                $commande->date_livraison_souhaitee = $data->date_livraison_souhaitee;
                $commande->client_nom = $data->client_nom;
                $commande->client_email = $data->client_email;
                $commande->nombre_marchands = $data->nombre_marchands;
                $commandesCollection->add($commande);
            }
        }
        return $commandesCollection;
    }
}
