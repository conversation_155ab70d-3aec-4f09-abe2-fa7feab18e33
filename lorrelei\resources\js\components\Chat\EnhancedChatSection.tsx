import React, { useState, useEffect, useRef } from 'react';
import { useReverbWebSocket } from '@/hooks/useReverbWebSocket';
import { Loader2, Send, Paperclip, X } from 'lucide-react';
import { Button } from '@/Components/ui/button';
import { Textarea } from '@/Components/ui/textarea';
import AttachmentDisplay from '@/components/AttachmentDisplay';

interface Message {
    id: number;
    message: string;
    auteur_type: 'client' | 'marchand';
    auteur_nom: string;
    created_at: string;
    pieces_jointes?: string[];
}

interface EnhancedChatSectionProps {
    conversationId: string;
    messages: Message[];
    currentUserId: string;
    onSendMessage: (message: string, files?: File[]) => Promise<void>;
    onImageClick?: (imageData: any) => void;
    isActive: boolean;
    className?: string;
}

export const EnhancedChatSection: React.FC<EnhancedChatSectionProps> = ({
    conversationId,
    messages: initialMessages,
    currentUserId,
    onSendMessage,
    onImageClick,
    isActive,
    className = '',
}) => {
    const [messages, setMessages] = useState<Message[]>(initialMessages);
    const [newMessage, setNewMessage] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [sending, setSending] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Hook WebSocket avec gestion automatique de la frappe
    const {
        typingUsers,
        formatTypingUsers,
        useTypingInput,
    } = useReverbWebSocket({
        conversationId,
        onMessage: (data) => {
            // Ajouter le nouveau message à la liste
            const newMsg: Message = {
                id: parseInt(data.message.id),
                message: data.message.message,
                auteur_type: data.message.auteur_type,
                auteur_nom: data.message.auteur_nom,
                created_at: data.message.created_at,
                pieces_jointes: data.message.pieces_jointes,
            };
            setMessages(prev => [...prev, newMsg]);
            scrollToBottom();
        },
    });

    // Hook pour gérer la frappe automatiquement
    const { handleInputChange, handleInputBlur } = useTypingInput();

    // Synchroniser les messages avec les props
    useEffect(() => {
        setMessages(initialMessages);
    }, [initialMessages]);

    // Faire défiler vers le bas automatiquement
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Gérer les changements dans l'input
    const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setNewMessage(value);
        handleInputChange(value);
    };

    // Gérer l'envoi du message
    const handleSendMessage = async () => {
        if (!newMessage.trim() && attachments.length === 0) return;

        setSending(true);
        try {
            await onSendMessage(newMessage, attachments);
            setNewMessage('');
            setAttachments([]);
            handleInputBlur(); // Arrêter l'indicateur de frappe
        } catch (error) {
            console.error('Erreur envoi message:', error);
        } finally {
            setSending(false);
        }
    };

    // Gérer l'appui sur Entrée
    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Gérer la sélection de fichiers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setAttachments(prev => [...prev, ...files]);
    };

    // Supprimer un fichier sélectionné
    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    // Formater la date
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 1) {
            return 'À l\'instant';
        } else if (diffInHours < 24) {
            return `Il y a ${Math.floor(diffInHours)}h`;
        } else {
            return date.toLocaleDateString('fr-FR', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    };

    // Filtrer les utilisateurs qui tapent (exclure l'utilisateur actuel)
    const otherTypingUsers = typingUsers.filter(user => user.user_id.toString() !== currentUserId);

    return (
        <div className={`flex flex-col h-full ${className}`}>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`flex ${message.auteur_type === 'client' ? 'justify-end' : 'justify-start'}`}
                    >
                        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.auteur_type === 'client'
                                ? 'bg-blue-500 text-white'
                                : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                        }`}>
                            <div className="text-sm">
                                {message.message}
                            </div>

                            <AttachmentDisplay
                                attachments={message.pieces_jointes || []}
                                messageId={message.id.toString()}
                                messageType="conversation"
                                onImageClick={onImageClick}
                            />

                            <div className={`text-xs mt-1 ${
                                message.auteur_type === 'client' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                            }`}>
                                {formatDate(message.created_at)}
                            </div>
                        </div>
                    </div>
                ))}
                
                {/* Indicateur "en train d'écrire" */}
                {otherTypingUsers.length > 0 && (
                    <div className="flex justify-start">
                        <div className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-4 py-2 rounded-lg text-sm animate-pulse">
                            <div className="flex items-center space-x-2">
                                <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span>{formatTypingUsers(otherTypingUsers)}</span>
                            </div>
                        </div>
                    </div>
                )}
                
                <div ref={messagesEndRef} />
            </div>

            {/* Zone d'envoi de message */}
            {isActive && (
                <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    {/* Pièces jointes sélectionnées */}
                    {attachments.length > 0 && (
                        <div className="mb-3 flex flex-wrap gap-2">
                            {attachments.map((file, index) => (
                                <div key={index} className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-1">
                                    <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-32">
                                        {file.name}
                                    </span>
                                    <button
                                        onClick={() => removeAttachment(index)}
                                        className="ml-2 text-gray-500 hover:text-red-500"
                                    >
                                        <X className="h-4 w-4" />
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}

                    <div className="flex items-end space-x-2">
                        <div className="flex-1">
                            <Textarea
                                placeholder="Tapez votre message..."
                                value={newMessage}
                                onChange={handleMessageChange}
                                onKeyDown={handleKeyPress}
                                onBlur={handleInputBlur}
                                className="min-h-[60px] resize-none"
                                disabled={sending}
                            />
                        </div>

                        <div className="flex flex-col space-y-2">
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileSelect}
                                multiple
                                className="hidden"
                                accept="image/*,.pdf,.doc,.docx,.txt"
                            />

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => fileInputRef.current?.click()}
                                disabled={sending}
                            >
                                <Paperclip className="h-4 w-4" />
                            </Button>

                            <Button
                                onClick={handleSendMessage}
                                disabled={sending || (!newMessage.trim() && attachments.length === 0)}
                                size="sm"
                            >
                                {sending ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                    <Send className="h-4 w-4" />
                                )}
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};
