<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ClientMarchandConversation extends Model
{
    use HasFactory, HasUuids;



    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'marchand_id',
        'commande_principale_id',
        'sous_commande_id',
        'produit_id',
        'sujet',
        'statut',
        'priorite',
        'type_conversation',
        'date_creation',
        'date_dernier_message',
        'date_fermeture',
        'cree_par_client_id',
        'ferme_par_client_id',
        'ferme_par_marchand_id',
        'nombre_messages_total',
        'nombre_messages_client',
        'nombre_messages_marchand',
        'messages_non_lus_client',
        'messages_non_lus_marchand',
        'metadata',
        'notification_client',
        'notification_marchand',
        'archivage_automatique',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_creation' => 'datetime',
        'date_dernier_message' => 'datetime',
        'date_fermeture' => 'datetime',
        'notification_client' => 'boolean',
        'notification_marchand' => 'boolean',
        'archivage_automatique' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Client participant à la conversation
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Marchand participant à la conversation
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Commande principale liée à la conversation
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Produit lié à la conversation
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Messages de la conversation
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ConversationMessage::class, 'conversation_id');
    }

    /**
     * Scopes
     */
    public function scopeActives($query)
    {
        return $query->where('statut', 'active');
    }

    public function scopeNonLues($query, $type = 'marchand')
    {
        $field = $type === 'marchand' ? 'lu_par_marchand' : 'lu_par_client';
        return $query->where($field, false);
    }

    public function scopePourMarchand($query, $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    public function scopePourClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Méthodes utilitaires
     */
    public function marquerCommeLue($type = 'marchand'): void
    {
        $field = $type === 'marchand' ? 'lu_par_marchand' : 'lu_par_client';
        $this->update([$field => true]);
    }

    public function ajouterMessage($contenu, $expediteur): void
    {
        $this->messages()->create([
            'contenu' => $contenu,
            'expediteur_type' => $expediteur === 'marchand' ? 'marchand' : 'client',
            'expediteur_id' => $expediteur === 'marchand' ? $this->marchand_id : $this->client_id,
            'date_envoi' => now(),
        ]);

        $this->update([
            'date_derniere_activite' => now(),
            'nombre_messages' => $this->messages()->count(),
            'lu_par_client' => $expediteur === 'client',
            'lu_par_marchand' => $expediteur === 'marchand',
        ]);
    }

    public function estActive(): bool
    {
        return $this->statut === 'active';
    }

    public function estNonLue($type = 'marchand'): bool
    {
        $field = $type === 'marchand' ? 'lu_par_marchand' : 'lu_par_client';
        return !$this->$field;
    }
}
