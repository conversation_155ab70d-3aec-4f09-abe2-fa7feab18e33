<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer les colonnes date_lecture_* si elles existent
        // car elles n'existent pas dans lorrelei/
        if (Schema::hasTable('dispute_messages')) {
            Schema::table('dispute_messages', function (Blueprint $table) {
                if (Schema::hasColumn('dispute_messages', 'date_lecture_admin')) {
                    $table->dropColumn('date_lecture_admin');
                }
                if (Schema::hasColumn('dispute_messages', 'date_lecture_client')) {
                    $table->dropColumn('date_lecture_client');
                }
                if (Schema::hasColumn('dispute_messages', 'date_lecture_marchand')) {
                    $table->dropColumn('date_lecture_marchand');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Ne rien faire car on ne veut pas recréer ces colonnes
    }
};
