<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MerchantValidationResource\Pages;
use App\Models\MerchantValidation;

use App\Services\MerchantValidationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MerchantValidationResource extends Resource
{
    protected static ?string $model = MerchantValidation::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'Validation Marchands';

    protected static ?string $modelLabel = 'Validation Marchand';

    protected static ?string $pluralModelLabel = 'Validations Marchands';

    protected static ?string $navigationGroup = 'Gestion des marchands';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations Utilisateur')
                    ->schema([
                        Forms\Components\TextInput::make('user.name')
                            ->label('Nom')
                            ->disabled(),
                        Forms\Components\TextInput::make('user.email')
                            ->label('Email')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('user.created_at')
                            ->label('Inscrit le')
                            ->disabled(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Informations Personnelles')
                    ->schema([
                        Forms\Components\KeyValue::make('personal_info')
                            ->label('Informations Personnelles')
                            ->disabled()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Forms\Get $get) => !empty($get('personal_info'))),

                Forms\Components\Section::make('Informations de Facturation')
                    ->schema([
                        Forms\Components\KeyValue::make('billing_info')
                            ->label('Informations de Facturation')
                            ->disabled()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Forms\Get $get) => !empty($get('billing_info'))),

                Forms\Components\Section::make('Informations Boutique')
                    ->schema([
                        Forms\Components\KeyValue::make('store_info')
                            ->label('Informations Boutique')
                            ->disabled()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Forms\Get $get) => !empty($get('store_info'))),

                Forms\Components\Section::make('Informations Business')
                    ->schema([
                        Forms\Components\KeyValue::make('business_info')
                            ->label('Informations Business')
                            ->disabled()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (Forms\Get $get) => !empty($get('business_info'))),

                Forms\Components\Section::make('Documents')
                    ->schema([
                        Forms\Components\Repeater::make('documents')
                            ->relationship()
                            ->schema([
                                Forms\Components\TextInput::make('type_document')
                                    ->label('Type de document')
                                    ->disabled(),
                                Forms\Components\TextInput::make('nom_fichier')
                                    ->label('Nom du fichier')
                                    ->disabled()
                                    ->suffixAction(
                                        Forms\Components\Actions\Action::make('download')
                                            ->icon('heroicon-m-arrow-down-tray')
                                            ->url(fn ($record) => $record ? route('admin.merchant-validation.download-document', [
                                                'validation' => $record->merchant_validation_id,
                                                'document' => $record->id
                                            ]) : null)
                                            ->openUrlInNewTab()
                                            ->visible(fn ($record) => $record && $record->file_path)
                                    ),
                                Forms\Components\TextInput::make('file_size')
                                    ->label('Taille')
                                    ->disabled()
                                    ->formatStateUsing(fn ($state) => $state ? number_format($state / 1024, 2) . ' KB' : 'N/A'),
                                Forms\Components\TextInput::make('created_at')
                                    ->label('Uploadé le')
                                    ->disabled()
                                    ->formatStateUsing(fn ($state) => $state ? $state->format('d/m/Y H:i') : 'N/A'),
                                Forms\Components\Select::make('status')
                                    ->label('Statut')
                                    ->options([
                                        'EN_ATTENTE' => 'En attente',
                                        'VALIDE' => 'Validé',
                                        'REJETE' => 'Rejeté',
                                    ])
                                    ->required(),
                                Forms\Components\Textarea::make('commentaire_validation')
                                    ->label('Commentaire')
                                    ->visible(fn (Forms\Get $get) => $get('status') === 'REJETE'),
                            ])
                            ->columns(3)
                            ->disabled(fn (string $operation) => $operation === 'view'),
                    ])
                    ->visible(fn ($record) => $record && $record->documents()->exists()),

                Forms\Components\Section::make('Validation')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('Statut')
                            ->options([
                                'EN_ATTENTE_SOUMISSION' => 'En attente de soumission',
                                'PERSONAL_INFO_COMPLETED' => 'Informations personnelles complétées',
                                'BILLING_INFO_COMPLETED' => 'Informations de facturation complétées',
                                'STORE_INFO_COMPLETED' => 'Informations boutique complétées',
                                'INFORMATIONS_SOUMISES' => 'Informations soumises',
                                'DOCUMENTS_SOUMIS' => 'Documents soumis',
                                'EN_ATTENTE_VALIDATION' => 'En attente de validation',
                                'VALIDE' => 'Validé',
                                'REJETE' => 'Rejeté',
                                'SUSPENDU' => 'Suspendu',
                            ])
                            ->required()
                            ->live(),
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Raison du rejet')
                            ->required()
                            ->visible(fn (Forms\Get $get) => $get('status') === 'REJETE'),
                        Forms\Components\Hidden::make('validated_by')
                            ->default(fn () => Auth::id()),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'EN_ATTENTE_SOUMISSION', 'PERSONAL_INFO_COMPLETED', 'BILLING_INFO_COMPLETED', 'STORE_INFO_COMPLETED' => 'warning',
                        'INFORMATIONS_SOUMISES', 'DOCUMENTS_SOUMIS' => 'info',
                        'EN_ATTENTE_VALIDATION' => 'primary',
                        'VALIDE' => 'success',
                        'REJETE' => 'danger',
                        'SUSPENDU' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'EN_ATTENTE_SOUMISSION' => 'En attente',
                        'PERSONAL_INFO_COMPLETED' => 'Info perso OK',
                        'BILLING_INFO_COMPLETED' => 'Facturation OK',
                        'STORE_INFO_COMPLETED' => 'Boutique OK',
                        'INFORMATIONS_SOUMISES' => 'Infos soumises',
                        'DOCUMENTS_SOUMIS' => 'Docs soumis',
                        'EN_ATTENTE_VALIDATION' => 'À valider',
                        'VALIDE' => 'Validé',
                        'REJETE' => 'Rejeté',
                        'SUSPENDU' => 'Suspendu',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('submitted_at')
                    ->label('Soumis le')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('validated_at')
                    ->label('Validé le')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('validator.name')
                    ->label('Validé par')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Statut')
                    ->options([
                        'EN_ATTENTE_VALIDATION' => 'En attente de validation',
                        'VALIDE' => 'Validé',
                        'REJETE' => 'Rejeté',
                        'SUSPENDU' => 'Suspendu',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Approuver')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(fn (MerchantValidation $record) => $record->status === 'EN_ATTENTE_VALIDATION')
                    ->action(function (MerchantValidation $record) {
                        $service = new MerchantValidationService();
                        $result = $service->approuverMarchand($record->id, Auth::id());
                        
                        if ($result['success']) {
                            Notification::make()
                                ->title('Marchand approuvé avec succès')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Erreur lors de l\'approbation')
                                ->body($result['message'] ?? 'Une erreur est survenue')
                                ->danger()
                                ->send();
                        }
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Rejeter')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Raison du rejet')
                            ->required()
                            ->maxLength(1000),
                    ])
                    ->visible(fn (MerchantValidation $record) => $record->status === 'EN_ATTENTE_VALIDATION')
                    ->action(function (MerchantValidation $record, array $data) {
                        $service = new MerchantValidationService();
                        $result = $service->rejeterMarchand($record->id, Auth::id(), $data['rejection_reason']);
                        
                        if ($result['success']) {
                            Notification::make()
                                ->title('Marchand rejeté')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Erreur lors du rejet')
                                ->body($result['message'] ?? 'Une erreur est survenue')
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMerchantValidations::route('/'),
            'create' => Pages\CreateMerchantValidation::route('/create'),
            'view' => Pages\ViewMerchantValidation::route('/{record}'),
            'edit' => Pages\EditMerchantValidation::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user', 'documents', 'validator']);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'EN_ATTENTE_VALIDATION')->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getNavigationBadge() > 0 ? 'warning' : null;
    }
}
