<?php

namespace App\Filament\Resources\BannerResource\Pages;

use App\Filament\Resources\BannerResource;
use App\Filament\Traits\HandlesImageStorage;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateBanner extends CreateRecord
{
    use HandlesImageStorage;

    protected static string $resource = BannerResource::class;

    protected function afterCreate(): void
    {
        // Déplacer les images du dossier temporaire vers le dossier basé sur l'ID
        self::moveImagesAfterCreate($this->record, 'banners', 'image_url');
    }
}
