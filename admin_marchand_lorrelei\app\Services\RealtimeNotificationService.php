<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RealtimeNotificationService
{
    /**
     * URL du webhook pour notifier admin_marchand_lorrelei
     */
    private string $webhookUrl;

    public function __construct()
    {
        $this->webhookUrl = config('app.url') . '/api/webhook/realtime-notification';
    }

    /**
     * Notifier un nouveau message dans une conversation
     */
    public function notifyNewMessage(array $messageData): bool
    {
        try {
            $payload = [
                'type' => 'new_message',
                'data' => $messageData,
                'timestamp' => now()->toISOString(),
            ];

            $response = Http::timeout(5)->post($this->webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Notification temps réel envoyée avec succès', ['type' => 'new_message']);
                return true;
            } else {
                Log::warning('Échec de la notification temps réel', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de la notification temps réel', [
                'error' => $e->getMessage(),
                'type' => 'new_message'
            ]);
            return false;
        }
    }

    /**
     * Notifier un nouveau litige
     */
    public function notifyNewDispute(array $disputeData): bool
    {
        try {
            $payload = [
                'type' => 'new_dispute',
                'data' => $disputeData,
                'timestamp' => now()->toISOString(),
            ];

            $response = Http::timeout(5)->post($this->webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Notification temps réel envoyée avec succès', ['type' => 'new_dispute']);
                return true;
            } else {
                Log::warning('Échec de la notification temps réel', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de la notification temps réel', [
                'error' => $e->getMessage(),
                'type' => 'new_dispute'
            ]);
            return false;
        }
    }

    /**
     * Notifier un nouveau message de litige
     */
    public function notifyNewDisputeMessage(array $messageData): bool
    {
        try {
            $payload = [
                'type' => 'new_dispute_message',
                'data' => $messageData,
                'timestamp' => now()->toISOString(),
            ];

            $response = Http::timeout(5)->post($this->webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Notification temps réel envoyée avec succès', ['type' => 'new_dispute_message']);
                return true;
            } else {
                Log::warning('Échec de la notification temps réel', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de la notification temps réel', [
                'error' => $e->getMessage(),
                'type' => 'new_dispute_message'
            ]);
            return false;
        }
    }

    /**
     * Notifier un changement de statut de litige
     */
    public function notifyDisputeStatusChange(array $disputeData): bool
    {
        try {
            $payload = [
                'type' => 'dispute_status_change',
                'data' => $disputeData,
                'timestamp' => now()->toISOString(),
            ];

            $response = Http::timeout(5)->post($this->webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Notification temps réel envoyée avec succès', ['type' => 'dispute_status_change']);
                return true;
            } else {
                Log::warning('Échec de la notification temps réel', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de la notification temps réel', [
                'error' => $e->getMessage(),
                'type' => 'dispute_status_change'
            ]);
            return false;
        }
    }
}
