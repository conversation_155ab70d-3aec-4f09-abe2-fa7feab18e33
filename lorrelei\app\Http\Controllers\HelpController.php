<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class HelpController extends Controller
{
    /**
     * Affiche le guide du marchand
     */
    public function guideMarchand(): Response
    {
        return Inertia::render('Help/GuideMarchand', [
            'title' => 'Guide du Marchand - Lorrelei',
            'description' => 'Tout ce que vous devez savoir pour réussir en tant que marchand sur Lorrelei',
        ]);
    }

    /**
     * Affiche le centre d'aide général
     */
    public function centreAide(): Response
    {
        return Inertia::render('Help/CentreAide', [
            'title' => 'Centre d\'Aide - Lorrelei',
            'description' => 'Trouvez des réponses à toutes vos questions sur Lorrelei',
        ]);
    }

    /**
     * Affiche la FAQ pour l'inscription marchand
     */
    public function faqInscription(): Response
    {
        return Inertia::render('Help/FaqInscription', [
            'title' => 'FAQ Inscription Marchand - Lorrelei',
            'description' => 'Questions fréquentes sur l\'inscription en tant que marchand',
        ]);
    }

    /**
     * Affiche la page de contact support
     */
    public function contactSupport(): Response
    {
        return Inertia::render('Help/ContactSupport', [
            'title' => 'Contacter le Support - Lorrelei',
            'description' => 'Contactez notre équipe support pour obtenir de l\'aide',
        ]);
    }

    /**
     * Traite l'envoi d'un message de support
     */
    public function envoyerMessageSupport(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'sujet' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'type_demande' => 'required|string|in:technique,commercial,inscription,autre',
        ]);

        // TODO: Implémenter l'envoi d'email au support
        // Pour l'instant, on simule l'envoi

        return back()->with('success', 'Votre message a été envoyé avec succès. Notre équipe vous répondra dans les plus brefs délais.');
    }
}
