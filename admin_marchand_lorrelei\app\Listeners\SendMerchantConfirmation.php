<?php

namespace App\Listeners;

use App\Events\MerchantApproved;
use App\Events\MerchantRejected;
use App\Notifications\MerchantApprovedNotification;
use App\Notifications\MerchantRejectedNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendMerchantConfirmation implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle merchant approved event.
     */
    public function handleApproved(MerchantApproved $event): void
    {
        try {
            $user = $event->validation->user;
            
            if (!$user) {
                Log::warning('Utilisateur non trouvé pour validation approuvée', [
                    'validation_id' => $event->validation->id,
                ]);
                return;
            }

            // Envoyer la notification d'approbation
            $user->notify(new MerchantApprovedNotification($event->validation, $event->marchand));

            Log::info('Notification d\'approbation envoyée au marchand', [
                'validation_id' => $event->validation->id,
                'user_id' => $user->id,
                'marchand_id' => $event->marchand?->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de notification d\'approbation', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle merchant rejected event.
     */
    public function handleRejected(MerchantRejected $event): void
    {
        try {
            $user = $event->validation->user;
            
            if (!$user) {
                Log::warning('Utilisateur non trouvé pour validation rejetée', [
                    'validation_id' => $event->validation->id,
                ]);
                return;
            }

            // Envoyer la notification de rejet
            $user->notify(new MerchantRejectedNotification($event->validation, $event->rejectionReason));

            Log::info('Notification de rejet envoyée au marchand', [
                'validation_id' => $event->validation->id,
                'user_id' => $user->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de notification de rejet', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, \Throwable $exception): void
    {
        $validationId = null;
        
        if ($event instanceof MerchantApproved || $event instanceof MerchantRejected) {
            $validationId = $event->validation->id;
        }

        Log::error('Échec définitif de l\'envoi de notification marchand', [
            'validation_id' => $validationId,
            'event_type' => get_class($event),
            'error' => $exception->getMessage(),
        ]);
    }
}
