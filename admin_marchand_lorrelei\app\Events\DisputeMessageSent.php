<?php

namespace App\Events;

use App\Models\DisputeMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DisputeMessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public DisputeMessage $message
    ) {
        $this->message->load(['dispute']);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('dispute.' . $this->message->dispute_id),
            new PrivateChannel('user.' . $this->message->dispute->client_id),
            new PrivateChannel('marchand.' . $this->message->dispute->marchand_id),
            new PrivateChannel('admin.notifications'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'dispute.message.sent';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'dispute_id' => $this->message->dispute_id,
                'message' => $this->message->message,
                'auteur_type' => $this->message->auteur_type,
                'auteur_id' => $this->message->auteur_id,
                'auteur_nom' => $this->message->auteur_nom,
                'type_message' => $this->message->type_message,
                'pieces_jointes' => $this->message->pieces_jointes,
                'interne' => $this->message->interne,
                'created_at' => $this->message->created_at->toISOString(),
            ],
            'dispute' => [
                'id' => $this->message->dispute->id,
                'numero_litige' => $this->message->dispute->numero_litige,
                'sujet' => $this->message->dispute->sujet,
                'statut' => $this->message->dispute->statut,
                'priorite' => $this->message->dispute->priorite,
                'urgent' => $this->message->dispute->urgent,
            ],
        ];
    }
}
