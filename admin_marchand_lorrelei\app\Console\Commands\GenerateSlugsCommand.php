<?php

namespace App\Console\Commands;

use App\Models\Categorie;
use App\Models\Produit;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateSlugsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-slugs {--model= : Le modèle pour lequel générer des slugs (categories, produits ou all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Génère des slugs pour les catégories et produits existants';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $model = $this->option('model') ?? 'all';

        if ($model === 'all' || $model === 'categories') {
            $this->generateSlugsForCategories();
        }

        if ($model === 'all' || $model === 'produits') {
            $this->generateSlugsForProduits();
        }

        $this->info('Génération des slugs terminée avec succès!');
    }

    /**
     * Génère des slugs pour les catégories existantes.
     */
    private function generateSlugsForCategories()
    {
        $this->info('Génération des slugs pour les catégories...');
        $categories = Categorie::whereNull('slug')->orWhere('slug', '')->get();
        $count = $categories->count();

        if ($count === 0) {
            $this->info('Aucune catégorie sans slug trouvée.');
            return;
        }

        $this->info("Génération de slugs pour {$count} catégories...");
        $bar = $this->output->createProgressBar($count);
        $bar->start();

        foreach ($categories as $categorie) {
            $slug = Str::slug($categorie->nom);
            $originalSlug = $slug;
            $counter = 1;

            // Vérifier si le slug existe déjà
            while (Categorie::where('slug', $slug)->where('id', '!=', $categorie->id)->exists()) {
                $slug = "{$originalSlug}-{$counter}";
                $counter++;
            }

            $categorie->slug = $slug;
            $categorie->save();
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("{$count} catégories ont été mises à jour avec des slugs.");
    }

    /**
     * Génère des slugs pour les produits existants.
     */
    private function generateSlugsForProduits()
    {
        $this->info('Génération des slugs pour les produits...');
        $produits = Produit::whereNull('slug')->orWhere('slug', '')->get();
        $count = $produits->count();

        if ($count === 0) {
            $this->info('Aucun produit sans slug trouvé.');
            return;
        }

        $this->info("Génération de slugs pour {$count} produits...");
        $bar = $this->output->createProgressBar($count);
        $bar->start();

        foreach ($produits as $produit) {
            $slug = Str::slug($produit->nom);
            $originalSlug = $slug;
            $counter = 1;

            // Vérifier si le slug existe déjà
            while (Produit::where('slug', $slug)->where('id', '!=', $produit->id)->exists()) {
                $slug = "{$originalSlug}-{$counter}";
                $counter++;
            }

            $produit->slug = $slug;
            $produit->save();
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("{$count} produits ont été mis à jour avec des slugs.");
    }
}
