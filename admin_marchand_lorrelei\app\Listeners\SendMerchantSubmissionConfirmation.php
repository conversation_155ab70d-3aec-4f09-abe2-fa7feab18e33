<?php

namespace App\Listeners;

use App\Events\MerchantSubmissionReceived;
use App\Notifications\MerchantSubmissionReceivedNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendMerchantSubmissionConfirmation implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MerchantSubmissionReceived $event): void
    {
        try {
            $user = $event->validation->user;
            
            if (!$user) {
                Log::warning('Utilisateur non trouvé pour confirmation de soumission', [
                    'validation_id' => $event->validation->id,
                ]);
                return;
            }

            // Envoyer la notification de confirmation au marchand
            $user->notify(new MerchantSubmissionReceivedNotification($event->validation));

            Log::info('Confirmation de soumission envoyée au marchand', [
                'validation_id' => $event->validation->id,
                'user_id' => $user->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de confirmation de soumission', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(MerchantSubmissionReceived $event, \Throwable $exception): void
    {
        Log::error('Échec définitif de l\'envoi de confirmation de soumission', [
            'validation_id' => $event->validation->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
