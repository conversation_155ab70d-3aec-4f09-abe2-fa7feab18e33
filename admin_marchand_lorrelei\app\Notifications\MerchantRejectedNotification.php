<?php

namespace App\Notifications;

use App\Models\MerchantValidation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MerchantRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public MerchantValidation $validation;
    public string $rejectionReason;

    /**
     * Create a new notification instance.
     */
    public function __construct(MerchantValidation $validation, string $rejectionReason)
    {
        $this->validation = $validation;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $businessInfo = $this->validation->business_info ?? [];
        $businessName = $businessInfo['nomEntreprise'] ?? 'votre boutique';

        return (new MailMessage)
            ->subject('📋 Mise à jour de votre demande marchand - Lorrelei')
            ->greeting('Bonjour ' . $notifiable->name . ',')
            ->line('Nous avons examiné votre demande d\'inscription en tant que marchand pour "' . $businessName . '".')
            ->line('Malheureusement, nous ne pouvons pas approuver votre demande pour le moment.')
            ->line('**Raison du refus :**')
            ->line($this->rejectionReason)
            ->line('**Que faire maintenant ?**')
            ->line('• Corrigez les points mentionnés ci-dessus')
            ->line('• Soumettez une nouvelle demande avec les informations mises à jour')
            ->line('• Contactez notre équipe support si vous avez des questions')
            ->action('Soumettre une nouvelle demande', route('seller.welcome'))
            ->line('**Besoin d\'aide ?**')
            ->line('Notre équipe support est là pour vous aider :')
            ->line('• Email : [<EMAIL>](mailto:<EMAIL>)')
            ->line('• Centre d\'aide : [https://lorrelei.com/aide](https://lorrelei.com/aide)')
            ->line('Nous espérons pouvoir vous accueillir prochainement dans la communauté Lorrelei.')
            ->salutation('Cordialement,<br>L\'équipe Lorrelei');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $businessInfo = $this->validation->business_info ?? [];
        
        return [
            'type' => 'merchant_rejected',
            'title' => 'Demande marchand refusée',
            'message' => 'Votre demande d\'inscription marchand a été refusée. Consultez les détails pour plus d\'informations.',
            'validation_id' => $this->validation->id,
            'business_name' => $businessInfo['nomEntreprise'] ?? 'Votre boutique',
            'rejection_reason' => $this->rejectionReason,
            'rejected_at' => $this->validation->rejected_at?->toISOString(),
            'action_url' => route('seller.welcome'),
        ];
    }
}
