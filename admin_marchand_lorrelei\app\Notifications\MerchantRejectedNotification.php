<?php

namespace App\Notifications;

use App\Models\MerchantValidation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MerchantRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public MerchantValidation $validation;
    public string $rejectionReason;

    /**
     * Create a new notification instance.
     */
    public function __construct(MerchantValidation $validation, string $rejectionReason)
    {
        $this->validation = $validation;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $businessInfo = $this->validation->business_info ?? [];
        $businessName = $businessInfo['nomEntreprise'] ?? 'votre boutique';

        return (new MailMessage)
            ->subject('📋 Mise à jour de votre demande marchand - Lorrelei')
            ->view('emails.merchant.rejected', [
                'user_name' => $notifiable->name,
                'business_name' => $businessName,
                'rejection_reason' => $this->rejectionReason,
                'resubmit_url' => route('seller.welcome'),
                'validation' => $this->validation,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $businessInfo = $this->validation->business_info ?? [];
        
        return [
            'type' => 'merchant_rejected',
            'title' => 'Demande marchand refusée',
            'message' => 'Votre demande d\'inscription marchand a été refusée. Consultez les détails pour plus d\'informations.',
            'validation_id' => $this->validation->id,
            'business_name' => $businessInfo['nomEntreprise'] ?? 'Votre boutique',
            'rejection_reason' => $this->rejectionReason,
            'rejected_at' => $this->validation->rejected_at?->toISOString(),
            'action_url' => route('seller.welcome'),
        ];
    }
}
