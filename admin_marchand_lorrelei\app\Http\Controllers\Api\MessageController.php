<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ClientMarchandConversation;
use App\Models\ConversationMessage;
use App\Services\ReverbWebSocketService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class MessageController extends Controller
{
    protected ReverbWebSocketService $webSocketService;

    public function __construct(ReverbWebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }
    /**
     * Liste des conversations du marchand connecté
     */
    public function index(Request $request)
    {
        $marchandId = Auth::user()->marchands->first()?->id;

        if (!$marchandId) {
            return response()->json(['error' => 'Marchand non trouvé'], 404);
        }

        $conversations = ClientMarchandConversation::with(['client.user'])
            ->where('marchand_id', $marchandId)
            ->when($request->search, function ($query, $search) {
                $query->where('sujet', 'like', "%{$search}%")
                      ->orWhereHas('client.user', function ($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%");
                      });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('statut', $status);
            })
            ->orderByDesc('date_dernier_message')
            ->paginate(20);

        return response()->json([
            'conversations' => $conversations->items(),
            'pagination' => [
                'current_page' => $conversations->currentPage(),
                'last_page' => $conversations->lastPage(),
                'total' => $conversations->total(),
            ]
        ]);
    }

    /**
     * Détails d'une conversation avec ses messages
     */
    public function show($id)
    {
        $marchandId = Auth::user()->marchands->first()?->id;

        $conversation = ClientMarchandConversation::with(['client.user', 'marchand'])
            ->where('id', $id)
            ->where('marchand_id', $marchandId)
            ->firstOrFail();

        $messages = ConversationMessage::where('conversation_id', $id)
            ->orderBy('created_at', 'asc')
            ->get();

        // Marquer les messages comme lus par le marchand
        ConversationMessage::where('conversation_id', $id)
            ->where('lu_par_marchand', false)
            ->update(['lu_par_marchand' => true]);

        // Mettre à jour le compteur de messages non lus
        $conversation->update(['messages_non_lus_marchand' => 0]);

        return response()->json([
            'conversation' => $conversation,
            'messages' => $messages
        ]);
    }

    /**
     * Envoyer un nouveau message
     */
    public function store(Request $request, $conversationId)
    {
        $request->validate([
            'message' => 'required|string|max:2000',
            'type_message' => 'required|string|in:message,reponse_question,information_produit,mise_a_jour_commande,resolution_probleme',
            'pieces_jointes' => 'nullable|array',
            'pieces_jointes.*' => 'file|max:5120|mimes:jpg,jpeg,png,pdf,doc,docx'
        ]);

        $marchandId = Auth::user()->marchands->first()?->id;

        $conversation = ClientMarchandConversation::where('id', $conversationId)
            ->where('marchand_id', $marchandId)
            ->firstOrFail();

        // Gérer les pièces jointes
        $attachments = [];
        if ($request->hasFile('pieces_jointes')) {
            foreach ($request->file('pieces_jointes') as $file) {
                $path = $file->store('conversation-attachments', 'private');
                $attachments[] = $path;
            }
        }

        // Créer le message
        $message = ConversationMessage::create([
            'conversation_id' => $conversationId,
            'auteur_type' => 'marchand',
            'auteur_marchand_id' => $marchandId,
            'auteur_nom' => Auth::user()->name,
            'message' => $request->message,
            'type_message' => $request->type_message,
            'pieces_jointes' => $attachments,
            'lu_par_marchand' => true,
        ]);

        // Mettre à jour la conversation
        $conversation->update([
            'date_dernier_message' => now(),
            'messages_non_lus_client' => $conversation->messages_non_lus_client + 1,
        ]);

        // Diffuser le message via WebSocket
        $this->webSocketService->broadcastMessage($message);

        // Envoyer webhook vers lorrelei/ pour notifier le client
        try {
            $webhookUrl = config('services.lorrelei.webhook_url');

            $payload = [
                'type' => 'new_message_from_admin',
                'data' => [
                    'id' => $message->id,
                    'conversation_id' => $message->conversation_id,
                    'auteur_type' => $message->auteur_type,
                    'auteur_nom' => $message->auteur_nom,
                    'message' => $message->message,
                    'type_message' => $message->type_message,
                    'pieces_jointes' => $message->pieces_jointes,
                    'created_at' => $message->created_at->toISOString(),
                    'conversation' => [
                        'id' => $conversation->id,
                        'client_id' => $conversation->client_id,
                        'marchand_id' => $conversation->marchand_id,
                        'sujet' => $conversation->sujet,
                    ]
                ],
                'timestamp' => now()->toISOString(),
            ];

            Http::timeout(5)->post($webhookUrl, $payload);
        } catch (\Exception $e) {
            Log::warning('Erreur envoi webhook vers lorrelei', [
                'error' => $e->getMessage(),
                'message_id' => $message->id
            ]);
        }

        return response()->json([
            'message' => $message,
            'success' => true
        ]);
    }

    /**
     * Fermer une conversation
     */
    public function close($id)
    {
        $marchandId = Auth::user()->marchands->first()?->id;

        $conversation = ClientMarchandConversation::where('id', $id)
            ->where('marchand_id', $marchandId)
            ->firstOrFail();

        $conversation->update([
            'statut' => 'fermee',
            'date_fermeture' => now(),
            'ferme_par_marchand_id' => $marchandId,
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Télécharger une pièce jointe
     */
    public function downloadAttachment($messageId, $filename)
    {
        $marchandId = Auth::user()->marchands->first()?->id;

        $message = ConversationMessage::whereHas('conversation', function ($query) use ($marchandId) {
            $query->where('marchand_id', $marchandId);
        })->findOrFail($messageId);

        $attachments = $message->pieces_jointes ?? [];
        $filePath = collect($attachments)->first(function ($path) use ($filename) {
            return basename($path) === $filename;
        });

        if (!$filePath || !Storage::disk('private')->exists($filePath)) {
            abort(404);
        }

        return response()->download(storage_path('app/private/' . $filePath));
    }

    /**
     * Statistiques des messages pour le dashboard
     */
    public function stats()
    {
        $marchandId = Auth::user()->marchands->first()?->id;

        if (!$marchandId) {
            return response()->json(['error' => 'Marchand non trouvé'], 404);
        }

        $stats = [
            'total_conversations' => ClientMarchandConversation::where('marchand_id', $marchandId)->count(),
            'conversations_actives' => ClientMarchandConversation::where('marchand_id', $marchandId)
                ->where('statut', 'active')->count(),
            'messages_non_lus' => ClientMarchandConversation::where('marchand_id', $marchandId)
                ->sum('messages_non_lus_marchand'),
            'conversations_aujourd_hui' => ClientMarchandConversation::where('marchand_id', $marchandId)
                ->whereDate('created_at', today())->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Endpoint pour l'état "en train d'écrire"
     */
    public function typing(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|string',
            'is_typing' => 'required|boolean',
        ]);

        $marchandId = Auth::user()->marchands->first()?->id;

        // Vérifier que le marchand a accès à cette conversation
        $conversation = ClientMarchandConversation::where('id', $request->conversation_id)
            ->where('marchand_id', $marchandId)
            ->firstOrFail();

        $this->webSocketService->broadcastTyping(
            'conversation',
            $request->conversation_id,
            $request->is_typing
        );

        return response()->json(['success' => true]);
    }
}
