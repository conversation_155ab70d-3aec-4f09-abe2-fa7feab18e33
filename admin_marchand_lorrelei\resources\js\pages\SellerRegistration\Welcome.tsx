import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Store, TrendingUp, Users, Shield } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import SellerHeader from '@/components/SellerHeader';

interface Props {
    user?: {
        id: number;
        name: string;
        email: string;
    };
}

export default function Welcome({ user }: Props) {
    const { translate } = useTranslation();

    const benefits = [
        {
            icon: Store,
            title: translate('seller.welcome.benefits.store.title'),
            description: translate('seller.welcome.benefits.store.description')
        },
        {
            icon: TrendingUp,
            title: translate('seller.welcome.benefits.sales.title'),
            description: translate('seller.welcome.benefits.sales.description')
        },
        {
            icon: Users,
            title: translate('seller.welcome.benefits.support.title'),
            description: translate('seller.welcome.benefits.support.description')
        },
        {
            icon: Shield,
            title: translate('seller.welcome.benefits.security.title'),
            description: translate('seller.welcome.benefits.security.description')
        }
    ];

    const steps = [
        translate('seller.welcome.steps.step1'),
        translate('seller.welcome.steps.step2'),
        translate('seller.welcome.steps.step3'),
        translate('seller.welcome.steps.step4')
    ];

    return (
        <>
            <Head title={String(translate('seller.welcome.title'))} />

            <div className="min-h-screen bg-background text-foreground">
                <SellerHeader />

                <div className="container mx-auto px-4 py-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-foreground mb-4">
                            {translate('seller.welcome.hero.title')}
                        </h1>
                        <p className="text-xl text-muted-foreground mb-2">
                            {translate('seller.welcome.hero.greeting', { name: user?.name || 'Marchand' })}
                        </p>
                        <p className="text-muted-foreground">
                            {translate('seller.welcome.hero.subtitle')}
                        </p>
                    </div>

                    {/* Benefits Section */}
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {benefits.map((benefit, index) => (
                            <Card key={index} className="text-center hover:shadow-lg transition-all duration-300 hover:scale-105 border-border bg-card">
                                <CardHeader>
                                    <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                        <benefit.icon className="w-6 h-6 text-primary" />
                                    </div>
                                    <CardTitle className="text-lg text-card-foreground">{benefit.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription className="text-muted-foreground">{benefit.description}</CardDescription>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Process Steps */}
                    <Card className="mb-8 border-border bg-card">
                        <CardHeader>
                            <CardTitle className="text-2xl text-center text-card-foreground">
                                {translate('seller.welcome.process.title')}
                            </CardTitle>
                            <CardDescription className="text-center text-muted-foreground">
                                {translate('seller.welcome.process.subtitle')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-4 gap-6">
                                {steps.map((step, index) => (
                                    <div key={index} className="text-center">
                                        <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-3 font-semibold shadow-lg">
                                            {index + 1}
                                        </div>
                                        <p className="text-sm text-muted-foreground">{step}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* CTA Section */}
                    <Card className="bg-primary text-primary-foreground border-primary">
                        <CardContent className="text-center py-8">
                            <h2 className="text-2xl font-bold mb-4">
                                {translate('seller.welcome.cta.title')}
                            </h2>
                            <p className="mb-6 text-primary-foreground/80">
                                {translate('seller.welcome.cta.description')}
                            </p>
                            <div className="space-y-4">
                                <Link href={route('seller.personal-info')}>
                                    <Button size="lg" variant="secondary" className="w-full md:w-auto">
                                        {translate('seller.welcome.cta.button')}
                                    </Button>
                                </Link>
                                <div className="flex items-center justify-center space-x-2 text-sm text-primary-foreground/80">
                                    <CheckCircle className="w-4 h-4" />
                                    <span>{translate('seller.welcome.cta.feature1')}</span>
                                    <CheckCircle className="w-4 h-4" />
                                    <span>{translate('seller.welcome.cta.feature2')}</span>
                                    <CheckCircle className="w-4 h-4" />
                                    <span>{translate('seller.welcome.cta.feature3')}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Footer */}
                    <div className="text-center mt-8 text-muted-foreground">
                        <p>
                            {translate('seller.welcome.footer.question')}{' '}
                            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
