import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
// Extend Window interface to include <PERSON>ush<PERSON>
declare global {
    interface Window {
        Pusher: typeof Pusher;
    }
}

// Configuration Pusher pour Reverb
window.Pusher = Pusher;

class ReverbWebSocketService {
    private echo: Echo<any> | null = null;
    private channels: Map<string, any> = new Map();
    private isConnected: boolean = false;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;

    constructor() {
        this.initializeEcho();
    }

    private initializeEcho(): void {
        try {
            this.echo = new Echo({
                broadcaster: 'reverb',
                key: import.meta.env.VITE_REVERB_APP_KEY,
                wsHost: import.meta.env.VITE_REVERB_HOST,
                wsPort: import.meta.env.VITE_REVERB_PORT,
                wssPort: import.meta.env.VITE_REVERB_PORT,
                forceTLS: import.meta.env.VITE_REVERB_SCHEME === 'https',
                enabledTransports: ['ws', 'wss'],
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Authorization': `Bearer ${this.getAuthToken()}`,
                    },
                },
                authEndpoint: '/broadcasting/auth',
            });

            this.setupConnectionEvents();
        } catch (error) {
            console.error('Failed to initialize Echo:', error);
            this.scheduleReconnect();
        }
    }

    private setupConnectionEvents(): void {
        if (!this.echo) return;

        this.echo.connector.pusher.connection.bind('connected', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            console.log('✅ WebSocket connected to Reverb');
            this.onConnectionChange?.(true);
        });

        this.echo.connector.pusher.connection.bind('disconnected', () => {
            this.isConnected = false;
            console.log('❌ WebSocket disconnected from Reverb');
            this.onConnectionChange?.(false);
            this.scheduleReconnect();
        });

        this.echo.connector.pusher.connection.bind('error', (error: any) => {
            console.error('WebSocket error:', error);
            this.scheduleReconnect();
        });
    }

    private scheduleReconnect(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

        setTimeout(() => {
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            this.initializeEcho();
        }, delay);
    }

    private getAuthToken(): string {
        // Récupérer le token d'authentification depuis le localStorage ou cookies
        return localStorage.getItem('auth_token') || '';
    }

    /**
     * S'abonner à un canal de conversation
     */
    subscribeToConversation(conversationId: string, callbacks: {
        onMessage?: (data: any) => void;
        onTyping?: (data: any) => void;
        onRead?: (data: any) => void;
    }): void {
        if (!this.echo) {
            console.error('Echo not initialized');
            return;
        }

        const channelName = `conversation.${conversationId}`;

        if (this.channels.has(channelName)) {
            console.log(`Already subscribed to ${channelName}`);
            return;
        }

        try {
            const channel = this.echo.private(channelName);

            if (callbacks.onMessage) {
                channel.listen('.message.sent', (data: any) => {
                    console.log('📨 New message received:', data);
                    callbacks.onMessage?.(data);
                    this.playNotificationSound();
                });
            }

            if (callbacks.onTyping) {
                channel.listen('.user.typing', (data: any) => {
                    console.log('✍️ User typing:', data);
                    callbacks.onTyping?.(data);
                });
            }

            if (callbacks.onRead) {
                channel.listen('.conversation.read', (data: any) => {
                    console.log('👁️ Conversation read:', data);
                    callbacks.onRead?.(data);
                });
            }

            this.channels.set(channelName, channel);
            console.log(`✅ Subscribed to conversation: ${conversationId}`);
        } catch (error) {
            console.error(`Failed to subscribe to conversation ${conversationId}:`, error);
        }
    }

    /**
     * S'abonner à un canal de litige
     */
    subscribeToDispute(disputeId: string, callbacks: {
        onMessage?: (data: any) => void;
        onStatusChange?: (data: any) => void;
        onTyping?: (data: any) => void;
    }): void {
        if (!this.echo) {
            console.error('Echo not initialized');
            return;
        }

        const channelName = `dispute.${disputeId}`;

        if (this.channels.has(channelName)) {
            console.log(`Already subscribed to ${channelName}`);
            return;
        }

        try {
            const channel = this.echo.private(channelName);

            if (callbacks.onMessage) {
                channel.listen('.dispute.message.sent', (data: any) => {
                    console.log('📨 New dispute message received:', data);
                    callbacks.onMessage?.(data);
                    this.playNotificationSound();
                });
            }

            if (callbacks.onStatusChange) {
                channel.listen('.dispute.status.changed', (data: any) => {
                    console.log('🔄 Dispute status changed:', data);
                    callbacks.onStatusChange?.(data);
                });
            }

            if (callbacks.onTyping) {
                channel.listen('.user.typing', (data: any) => {
                    console.log('✍️ User typing in dispute:', data);
                    callbacks.onTyping?.(data);
                });
            }

            this.channels.set(channelName, channel);
            console.log(`✅ Subscribed to dispute: ${disputeId}`);
        } catch (error) {
            console.error(`Failed to subscribe to dispute ${disputeId}:`, error);
        }
    }

    /**
     * S'abonner aux notifications utilisateur
     */
    subscribeToUserNotifications(userId: string, callbacks: {
        onNotification?: (data: any) => void;
    }): void {
        if (!this.echo) {
            console.error('Echo not initialized');
            return;
        }

        const channelName = `user.${userId}`;

        if (this.channels.has(channelName)) {
            return;
        }

        try {
            const channel = this.echo.private(channelName);

            if (callbacks.onNotification) {
                channel.listen('.notification', (data: any) => {
                    console.log('🔔 New notification:', data);
                    callbacks.onNotification?.(data);
                    this.showBrowserNotification(data);
                });
            }

            this.channels.set(channelName, channel);
            console.log(`✅ Subscribed to user notifications: ${userId}`);
        } catch (error) {
            console.error(`Failed to subscribe to user notifications ${userId}:`, error);
        }
    }

    /**
     * Envoyer l'état "en train d'écrire"
     */
    async sendTyping(conversationId: string, isTyping: boolean = true): Promise<void> {
        // TODO: Activer quand le backend aura la route /api/messages/typing
        if (process.env.NODE_ENV === 'development') {
            console.log(`[WebSocket] Typing ${isTyping ? 'started' : 'stopped'} for conversation ${conversationId}`);
            return;
        }

        try {
            const response = await fetch('/api/messages/typing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    conversation_id: conversationId,
                    is_typing: isTyping,
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            // Silencieux en développement pour éviter le spam de logs
            if (process.env.NODE_ENV !== 'development') {
                console.error('Failed to send typing status:', error);
            }
        }
    }

    /**
     * Se désabonner d'un canal
     */
    unsubscribe(channelName: string): void {
        const channel = this.channels.get(channelName);
        if (channel && this.echo) {
            this.echo.leave(channelName);
            this.channels.delete(channelName);
            console.log(`❌ Unsubscribed from: ${channelName}`);
        }
    }

    /**
     * Se désabonner de tous les canaux
     */
    unsubscribeAll(): void {
        this.channels.forEach((channel, channelName) => {
            this.unsubscribe(channelName);
        });
    }

    /**
     * Se déconnecter complètement
     */
    disconnect(): void {
        this.unsubscribeAll();
        if (this.echo) {
            this.echo.disconnect();
            this.echo = null;
        }
        this.isConnected = false;
        console.log('🔌 WebSocket disconnected');
    }

    /**
     * Jouer un son de notification
     */
    private playNotificationSound(): void {
        try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(() => {
                // Ignorer les erreurs de lecture audio (autoplay policy)
            });
        } catch (error) {
            // Ignorer les erreurs audio
        }
    }

    /**
     * Afficher une notification navigateur
     */
    private showBrowserNotification(data: any): void {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(data.title || 'Nouveau message', {
                body: data.message || 'Vous avez reçu un nouveau message',
                icon: '/favicon.ico',
                tag: data.id || 'notification',
            });
        }
    }

    /**
     * Demander la permission pour les notifications
     */
    async requestNotificationPermission(): Promise<boolean> {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }

    /**
     * Callback pour les changements de connexion
     */
    public onConnectionChange?: (connected: boolean) => void;

    /**
     * Getters
     */
    get connected(): boolean {
        return this.isConnected;
    }

    get activeChannels(): string[] {
        return Array.from(this.channels.keys());
    }
}

export default new ReverbWebSocketService();
