<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\Commande;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class SalesChart extends ChartWidget
{
    protected static ?string $heading = 'Ventes des 30 derniers jours (Multi-Marchands)';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $marchandId = auth()->user()->marchands->first()->id ?? null;

        if (!$marchandId) {
            return ['datasets' => [], 'labels' => []];
        }

        // Données des sous-commandes (nouveau système)
        $sousCommandesData = DB::connection('lorrelei')
            ->table('sous_commandes_vendeur')
            ->where('marchand_id', $marchandId)
            ->where('created_at', '>=', now()->subDays(30))
            ->whereIn('statut', ['EnAttente', 'EnTraitement', 'Expédié', 'Livré'])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(montant_versement_marchand) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Données des commandes legacy (ancien système)
        $commandesLegacyData = Commande::where('marchand_id', $marchandId)
            ->where('type_commande', 'legacy')
            ->where('created_at', '>=', now()->subDays(30))
            ->whereIn('statut', ['EnAttente', 'EnCoursDeTraitement', 'Expédié', 'Livré'])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $dates = [];
        $sousCommandesTotals = [];
        $legacyTotals = [];
        $combinedTotals = [];

        // Créer un tableau avec tous les jours des 30 derniers jours
        for ($i = 30; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dates[] = now()->subDays($i)->format('d/m');

            // Données sous-commandes
            $sousCommandeDay = $sousCommandesData->firstWhere('date', $date);
            $sousCommandeTotal = $sousCommandeDay ? $sousCommandeDay->total : 0;
            $sousCommandesTotals[] = $sousCommandeTotal;

            // Données legacy
            $legacyDay = $commandesLegacyData->firstWhere('date', $date);
            $legacyTotal = $legacyDay ? $legacyDay->total : 0;
            $legacyTotals[] = $legacyTotal;

            // Total combiné
            $combinedTotals[] = $sousCommandeTotal + $legacyTotal;
        }
        
        return [
            'datasets' => [
                [
                    'label' => 'Sous-commandes (FCFA)',
                    'data' => $sousCommandesTotals,
                    'fill' => false,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'tension' => 0.1,
                ],
                [
                    'label' => 'Commandes legacy (FCFA)',
                    'data' => $legacyTotals,
                    'fill' => false,
                    'backgroundColor' => 'rgba(249, 115, 22, 0.1)',
                    'borderColor' => 'rgb(249, 115, 22)',
                    'tension' => 0.1,
                ],
                [
                    'label' => 'Total (FCFA)',
                    'data' => $combinedTotals,
                    'fill' => 'start',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'tension' => 0.1,
                    'borderWidth' => 3,
                ],
            ],
            'labels' => $dates,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
