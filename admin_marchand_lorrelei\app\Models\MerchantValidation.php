<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MerchantValidation extends Model
{
    protected $fillable = [
        'user_id',
        'status',
        'personal_info',
        'billing_info',
        'store_info',
        'business_info',
        'rejection_reason',
        'submitted_at',
        'validated_at',
        'rejected_at',
        'validated_by',
    ];

    protected $casts = [
        'personal_info' => 'array',
        'billing_info' => 'array',
        'store_info' => 'array',
        'business_info' => 'array',
        'submitted_at' => 'datetime',
        'validated_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    // Constantes pour les statuts
    const STATUS_EN_ATTENTE_SOUMISSION = 'EN_ATTENTE_SOUMISSION';
    const STATUS_PERSONAL_INFO_COMPLETED = 'PERSONAL_INFO_COMPLETED';
    const STATUS_BILLING_INFO_COMPLETED = 'BILLING_INFO_COMPLETED';
    const STATUS_STORE_INFO_COMPLETED = 'STORE_INFO_COMPLETED';
    const STATUS_INFORMATIONS_SOUMISES = 'INFORMATIONS_SOUMISES';
    const STATUS_DOCUMENTS_SOUMIS = 'DOCUMENTS_SOUMIS';
    const STATUS_EN_ATTENTE_VALIDATION = 'EN_ATTENTE_VALIDATION';
    const STATUS_VALIDE = 'VALIDE';
    const STATUS_REJETE = 'REJETE';
    const STATUS_SUSPENDU = 'SUSPENDU';

    // Relations
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(MerchantValidationDocument::class, 'validation_id');
    }

    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    // Méthodes utilitaires
    public function isComplete(): bool
    {
        return $this->status === self::STATUS_DOCUMENTS_SOUMIS ||
               $this->status === self::STATUS_EN_ATTENTE_VALIDATION ||
               $this->status === self::STATUS_VALIDE;
    }

    public function canBeValidated(): bool
    {
        return $this->status === self::STATUS_EN_ATTENTE_VALIDATION &&
               $this->documents()->where('status', 'EN_ATTENTE')->doesntExist();
    }

    public function isPending(): bool
    {
        return in_array($this->status, [
            self::STATUS_EN_ATTENTE_SOUMISSION,
            self::STATUS_PERSONAL_INFO_COMPLETED,
            self::STATUS_BILLING_INFO_COMPLETED,
            self::STATUS_STORE_INFO_COMPLETED,
            self::STATUS_INFORMATIONS_SOUMISES,
            self::STATUS_DOCUMENTS_SOUMIS,
            self::STATUS_EN_ATTENTE_VALIDATION,
        ]);
    }

    /**
     * Obtient les documents requis selon le type de business
     */
    public function getRequiredDocuments(): array
    {
        $billingInfo = $this->billing_info;
        $typeBusiness = $billingInfo['type_business'] ?? 'individuel';

        $documentsBase = ['piece_identite', 'photo_avec_piece'];

        switch ($typeBusiness) {
            case 'individuel':
                return array_merge($documentsBase, ['justificatif_domicile']);

            case 'entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire',
                    'justificatif_domicile'
                ]);

            case 'cooperative':
                return array_merge($documentsBase, [
                    'recepisse_declaration',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            case 'grande_entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'bilan_comptable',
                    'rib_bancaire',
                    'declaration_fiscale'
                ]);

            default:
                return $documentsBase;
        }
    }

    /**
     * Vérifie si toutes les étapes sont complétées
     */
    public function isAllStepsCompleted(): bool
    {
        return !empty($this->personal_info) &&
               !empty($this->billing_info) &&
               !empty($this->store_info);
    }
}
