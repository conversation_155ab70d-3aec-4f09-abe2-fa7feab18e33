<?php

namespace App\Filament\Marchand\Resources;

use App\Models\MarchandAbonnement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Marchand\Resources\AbonnementResource\Pages;

class AbonnementResource extends Resource
{
    protected static ?string $model = MarchandAbonnement::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-euro';
    protected static ?string $navigationGroup = 'Mon Abonnement';
    protected static ?string $label = 'Abonnement';
    protected static ?string $pluralLabel = 'Abonnements';

    public static function form(Form $form): Form
    {
        return $form->schema([
            // À compléter selon les besoins
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('id')->sortable(),
            Tables\Columns\TextColumn::make('type_abonnement')
                ->label('Type')
                ->badge()
                ->color(fn (string $state): string =>match(true){
                    str_starts_with($state, 'elite') => 'elite',
                    str_starts_with($state, 'premium') => 'premium',
                    str_starts_with($state, 'basic') => 'basic',
                    str_starts_with($state, 'gratuit') => 'gratuit',
                    default => 'gray',
                }),
            Tables\Columns\TextColumn::make('statut')
                ->label('Statut')
                ->badge()
                ->color('active'),
            Tables\Columns\TextColumn::make('date_debut')->label('Début')->dateTime(),
            Tables\Columns\TextColumn::make('date_fin')->label('Fin')->dateTime(),
            Tables\Columns\TextColumn::make('prix_mensuel')->label('Prix mensuel'),
        ])
        ->filters([
            Tables\Filters\SelectFilter::make('statut')
                ->options([
                    'actif' => 'Actif',
                    'expire' => 'Expiré',
                    'suspendu' => 'Suspendu',
                    'annule' => 'Annulé',
                    'en_attente' => 'En attente',
                ]),
            Tables\Filters\SelectFilter::make('type_abonnement')
                ->options([
                    'gratuit' => 'Gratuit',
                    'basique' => 'Basique',
                    'premium' => 'Premium',
                    'elite' => 'Elite',
                ]),
        ])
        ->actions([
            Tables\Actions\EditAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
            ]),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAbonnements::route('/'),
        ];
    }
}
