<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class AdminNotificationService
{
    /**
     * URL du webhook admin_marchand_lorrelei
     */
    private string $webhookUrl;

    public function __construct()
    {
        // URL du webhook admin_marchand_lorrelei
        $this->webhookUrl = config('services.admin_webhook.url', 'http://localhost:8001/api/webhook/realtime-notification');
    }

    /**
     * Notifier un nouveau message de conversation
     */
    public function notifyNewMessage($message): bool
    {
        try {
            $payload = [
                'type' => 'new_message',
                'data' => [
                    'id' => $message->id,
                    'conversation_id' => $message->conversation_id,
                    'auteur_type' => $message->auteur_type,
                    'auteur_nom' => $message->auteur_nom,
                    'message' => $message->message,
                    'type_message' => $message->type_message,
                    'pieces_jointes' => $message->pieces_jointes,
                    'created_at' => $message->created_at->toISOString(),
                    'conversation' => [
                        'id' => $message->conversation->id,
                        'client_id' => $message->conversation->client_id,
                        'marchand_id' => $message->conversation->marchand_id,
                        'statut' => $message->conversation->statut,
                    ]
                ],
                'timestamp' => now()->toISOString(),
            ];

            return $this->sendWebhook($payload);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la notification de nouveau message', [
                'error' => $e->getMessage(),
                'message_id' => $message->id ?? null
            ]);
            return false;
        }
    }

    /**
     * Notifier un nouveau litige
     */
    public function notifyNewDispute($dispute): bool
    {
        try {
            $payload = [
                'type' => 'new_dispute',
                'data' => [
                    'id' => $dispute->id,
                    'numero_litige' => $dispute->numero_litige,
                    'type_litige' => $dispute->type_litige,
                    'statut' => $dispute->statut,
                    'priorite' => $dispute->priorite,
                    'urgent' => $dispute->urgent,
                    'description' => $dispute->description,
                    'montant_conteste' => $dispute->montant_conteste,
                    'created_at' => $dispute->created_at->toISOString(),
                    'client' => [
                        'id' => $dispute->client->id,
                        'user' => [
                            'id' => $dispute->client->user->id,
                            'name' => $dispute->client->user->name,
                            'email' => $dispute->client->user->email,
                        ]
                    ],
                    'marchand' => $dispute->marchand ? [
                        'id' => $dispute->marchand->id,
                        'nom_boutique' => $dispute->marchand->nom_boutique,
                    ] : null,
                ],
                'timestamp' => now()->toISOString(),
            ];

            return $this->sendWebhook($payload);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la notification de nouveau litige', [
                'error' => $e->getMessage(),
                'dispute_id' => $dispute->id ?? null
            ]);
            return false;
        }
    }

    /**
     * Notifier un nouveau message de litige
     */
    public function notifyNewDisputeMessage($message): bool
    {
        try {
            $payload = [
                'type' => 'new_dispute_message',
                'data' => [
                    'id' => $message->id,
                    'dispute_id' => $message->dispute_id,
                    'auteur_type' => $message->auteur_type,
                    'auteur_nom' => $message->auteur_nom,
                    'message' => $message->message,
                    'type_message' => $message->type_message,
                    'pieces_jointes' => $message->pieces_jointes,
                    'interne' => $message->interne,
                    'created_at' => $message->created_at->toISOString(),
                    'dispute' => [
                        'id' => $message->dispute->id,
                        'numero_litige' => $message->dispute->numero_litige,
                        'statut' => $message->dispute->statut,
                    ]
                ],
                'timestamp' => now()->toISOString(),
            ];

            return $this->sendWebhook($payload);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la notification de nouveau message de litige', [
                'error' => $e->getMessage(),
                'message_id' => $message->id ?? null
            ]);
            return false;
        }
    }

    /**
     * Notifier un changement de statut de litige
     */
    public function notifyDisputeStatusChange($dispute, $oldStatus): bool
    {
        try {
            $payload = [
                'type' => 'dispute_status_change',
                'data' => [
                    'id' => $dispute->id,
                    'numero_litige' => $dispute->numero_litige,
                    'ancien_statut' => $oldStatus,
                    'nouveau_statut' => $dispute->statut,
                    'updated_at' => $dispute->updated_at->toISOString(),
                    'client' => [
                        'id' => $dispute->client->id,
                        'user' => [
                            'name' => $dispute->client->user->name,
                        ]
                    ],
                ],
                'timestamp' => now()->toISOString(),
            ];

            return $this->sendWebhook($payload);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la notification de changement de statut', [
                'error' => $e->getMessage(),
                'dispute_id' => $dispute->id ?? null
            ]);
            return false;
        }
    }

    /**
     * Envoyer le webhook
     */
    private function sendWebhook(array $payload): bool
    {
        try {
            $response = Http::timeout(5)
                ->retry(2, 1000) // 2 tentatives avec 1s d'attente
                ->post($this->webhookUrl, $payload);

            if ($response->successful()) {
                Log::info('Webhook envoyé avec succès', [
                    'type' => $payload['type'],
                    'url' => $this->webhookUrl
                ]);
                return true;
            } else {
                Log::warning('Échec du webhook', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'type' => $payload['type']
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi du webhook', [
                'error' => $e->getMessage(),
                'type' => $payload['type'] ?? 'unknown',
                'url' => $this->webhookUrl
            ]);
            return false;
        }
    }
}
