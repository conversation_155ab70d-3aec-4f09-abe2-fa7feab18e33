<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('merchant_validations', function (Blueprint $table) {
            $table->json('personal_info')->nullable()->after('status');
            $table->json('billing_info')->nullable()->after('personal_info');
            $table->json('store_info')->nullable()->after('billing_info');
        });

        // Mettre à jour les statuts possibles
        DB::statement("ALTER TABLE merchant_validations MODIFY COLUMN status ENUM(
            'EN_ATTENTE_SOUMISSION',
            'PERSONAL_INFO_COMPLETED',
            'BILLING_INFO_COMPLETED',
            'STORE_INFO_COMPLETED',
            'INFORMATIONS_SOUMISES',
            'DOCUMENTS_SOUMIS',
            'EN_ATTENTE_VALIDATION',
            'VALIDE',
            'REJETE',
            'SUSPENDU'
        ) DEFAULT 'EN_ATTENTE_SOUMISSION'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchant_validations', function (Blueprint $table) {
            $table->dropColumn(['personal_info', 'billing_info', 'store_info']);
        });

        // Restaurer les anciens statuts
        DB::statement("ALTER TABLE merchant_validations MODIFY COLUMN status ENUM(
            'EN_ATTENTE_SOUMISSION',
            'INFORMATIONS_SOUMISES',
            'DOCUMENTS_SOUMIS',
            'EN_ATTENTE_VALIDATION',
            'VALIDE',
            'REJETE',
            'SUSPENDU'
        ) DEFAULT 'EN_ATTENTE_SOUMISSION'");
    }
};
