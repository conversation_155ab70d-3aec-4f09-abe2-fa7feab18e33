<?php

namespace App\Filament\Marchand\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LatestOrders extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    protected static ?string $heading = 'Commandes récentes (Multi-Marchands)';

    public function table(Table $table): Table
    {
        $marchandId = Auth::user()->marchand?->id;

        return $table
            ->query(
                // Requête cross-database pour récupérer les sous-commandes du marchand
                DB::connection('lorrelei')
                    ->table('sous_commandes_vendeur as scv')
                    ->join('commandes_principales as cp', 'scv.commande_principale_id', '=', 'cp.id')
                    ->join('clients as c', 'cp.client_id', '=', 'c.id')
                    ->join('users as u', 'c.user_id', '=', 'u.id')
                    ->where('scv.marchand_id', $marchandId)
                    ->select([
                        'scv.id',
                        'scv.numero_sous_commande',
                        'scv.montant_total',
                        'scv.statut',
                        'scv.created_at',
                        'scv.date_livraison_prevue',
                        'cp.numero_commande as commande_principale',
                        DB::raw("CONCAT(c.prenom, ' ', c.nom) as client_nom"),
                        'u.email as client_email',
                        'scv.frais_livraison'
                    ])
                    ->orderByDesc('scv.created_at')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Numéro')
                    ->formatStateUsing(fn ($state): string => "CMD-{$state}")
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->copyMessageDuration(1500),

                Tables\Columns\TextColumn::make('type_commande')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'legacy' => 'warning',
                        'multi_marchand' => 'success',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'legacy' => 'Legacy',
                        'multi_marchand' => 'Multi-Marchand',
                        default => ucfirst($state)
                    }),

                Tables\Columns\TextColumn::make('montantTotal')
                    ->label('Montant')
                    ->money('XOF')
                    ->sortable(),

                Tables\Columns\TextColumn::make('statut')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'EnAttente' => 'warning',
                        'en_attente' => 'warning',
                        'EnCoursDeTraitement' => 'primary',
                        'EnTraitement' => 'primary',
                        'Expédié' => 'info',
                        'Livré' => 'success',
                        'Annulé' => 'danger',
                        'Remboursé' => 'danger',
                        default => 'gray'
                    })
                    ->icon(fn (string $state): string => match($state) {
                        'EnAttente' => 'heroicon-m-clock',
                        'en_attente' => 'heroicon-m-clock',
                        'EnCoursDeTraitement' => 'heroicon-m-arrow-path',
                        'EnTraitement' => 'heroicon-m-arrow-path',
                        'Expédié' => 'heroicon-m-truck',
                        'Livré' => 'heroicon-m-check-circle',
                        'Annulé' => 'heroicon-m-x-circle',
                        'Remboursé' => 'heroicon-m-x-circle',
                        default => 'heroicon-m-question-mark-circle'
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'EnAttente' => 'En attente',
                        'en_attente' => 'En attente',
                        'EnCoursDeTraitement' => 'En traitement',
                        'EnTraitement' => 'En traitement',
                        'Expédié' => 'Expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Remboursé' => 'Remboursé',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('creeLe')
                    ->label('Date de création')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->default(fn($record) => $record->created_at?->format('d/m/Y H:i')),

                Tables\Columns\TextColumn::make('client.user.name')
                    ->label('Client')
                    ->searchable()
                    ->default('Client inconnu'),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Voir')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->url(function ($record): string {
                        return route('filament.marchand.resources.commandes.view', ['record' => $record->id]);
                    }),
            ])
            ->emptyStateHeading('Aucune commande')
            ->emptyStateDescription('Vos commandes apparaîtront ici.')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }
}
