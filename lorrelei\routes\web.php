<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Filament\Resources\ImportResource\Pages\ImportIndex as AdminImportIndex;
use App\Filament\Marchand\Resources\ImportResource\Pages\ImportIndex as MerchantImportIndex;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\Api\PayementController;
use App\Http\Controllers\DebugController;

// Routes originales du starter kit
Route::get('/welcome', function () {
    return redirect()->route('home');
})->name('welcome');

Route::middleware(['auth', 'verified'])->group(function () {
    // Routes du dashboard client
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard/orders', [\App\Http\Controllers\DashboardController::class, 'orders'])->name('dashboard.orders');
    Route::get('dashboard/orders/{numeroCommande}', [\App\Http\Controllers\DashboardController::class, 'orderDetails'])->name('dashboard.order-details');
    Route::get('dashboard/profile', [\App\Http\Controllers\DashboardController::class, 'profile'])->name('dashboard.profile');

    // Route pour compléter le profil
    Route::post('profile/complete', [\App\Http\Controllers\DashboardController::class, 'completeProfile'])->name('profile.complete');

    // Redirection de /profile vers /settings/profile
    Route::redirect('profile', 'settings/profile');

    // Routes de checkout (nouveau système)
    Route::prefix('checkout')->group(function () {
        Route::post('/create-order', [\App\Http\Controllers\CheckoutController::class, 'createOrder'])->name('checkout.create-order');
        Route::post('/confirm-payment', [\App\Http\Controllers\CheckoutController::class, 'confirmPayment'])->name('checkout.confirm-payment');
        Route::post('/cancel-order', [\App\Http\Controllers\CheckoutController::class, 'cancelOrder'])->name('checkout.cancel-order');
        Route::get('/order/{commandeId}', [\App\Http\Controllers\CheckoutController::class, 'getOrderDetails'])->name('checkout.order-details');
    });

    // Routes de paiement (legacy - à migrer progressivement)
    Route::prefix('payment')->group(function () {
        Route::post('/create-order', [PayementController::class, 'createOrder'])->name('payment.create-order');
        Route::post('/execute', [PayementController::class, 'executePayment'])->name('payment.execute');
        Route::get('/details/{paymentId}', [PayementController::class, 'getPaymentDetails'])->name('payment.details');
        Route::post('/refund', [PayementController::class, 'refundPayment'])->name('payment.refund');
        Route::get('/success', [PayementController::class, 'success'])->name('payment.success');
        Route::get('/cancel', [PayementController::class, 'cancel'])->name('payment.cancel');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// Les routes e-commerce sont à la racine du site
require __DIR__.'/ecommerce.php';

// Routes d'administration
require __DIR__.'/admin.php';

// Routes pour les modèles d'importation
Route::get('/admin/importations/download-categories-template', [AdminImportIndex::class, 'downloadCategoriesTemplate'])
    ->name('filament.admin.resources.importations.download-categories-template')
    ->middleware(['web', 'auth']);

Route::get('/admin/importations/download-products-template', [AdminImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.admin.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

// Route fallback pour les pages 404
Route::fallback(function () {
    return Inertia::render('not-found');
});

Route::get('/merchant/importations/download-products-template', [MerchantImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.marchand.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

// Routes pour la gestion des devises
Route::post('/currency/change', [CurrencyController::class, 'change'])->name('currency.change');

// Routes d'aide et support
Route::prefix('help')->name('help.')->group(function () {
    Route::get('/guide-marchand', [\App\Http\Controllers\HelpController::class, 'guideMarchand'])->name('guide-marchand');
    Route::get('/centre-aide', [\App\Http\Controllers\HelpController::class, 'centreAide'])->name('centre-aide');
    Route::get('/faq-inscription', [\App\Http\Controllers\HelpController::class, 'faqInscription'])->name('faq-inscription');
    Route::get('/contact-support', [\App\Http\Controllers\HelpController::class, 'contactSupport'])->name('contact-support');
    Route::post('/contact-support', [\App\Http\Controllers\HelpController::class, 'envoyerMessageSupport'])->name('contact-support.send');
});

// Routes d'aide accessibles directement (pour les liens dans les emails)
Route::get('/guide-marchand', [\App\Http\Controllers\HelpController::class, 'guideMarchand'])->name('guide-marchand');
Route::get('/aide', [\App\Http\Controllers\HelpController::class, 'centreAide'])->name('aide');
Route::get('/faq-inscription', [\App\Http\Controllers\HelpController::class, 'faqInscription'])->name('faq-inscription');

// Routes de debug (à supprimer en production)
Route::middleware(['auth'])->prefix('debug')->group(function () {
    Route::get('/services', [DebugController::class, 'testServices'])->name('debug.services');
    Route::post('/order-creation', [DebugController::class, 'testOrderCreation'])->name('debug.order-creation');
    Route::get('/test-email', [\App\Http\Controllers\TestEmailController::class, 'testEmail'])->name('debug.test-email');
});



