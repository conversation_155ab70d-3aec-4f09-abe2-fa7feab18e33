[2025-06-16 23:24:28] local.ERROR: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (rib_bancaire, merchant-documents/4YlBzn28mz12Xy82IArXjGtqWo6UFqEDASMyR9rn.pdf, Application de Musique Récompensée.pdf, application/pdf, 181794, EN_ATTENTE, 1, 2025-06-16 23:24:28, 2025-06-16 23:24:28)) {"userId":14,"exception":"[object] (Illuminate\\Database\\QueryException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (rib_bancaire, merchant-documents/4YlBzn28mz12Xy82IArXjGtqWo6UFqEDASMyR9rn.pdf, Application de Musique Récompensée.pdf, application/pdf, 181794, EN_ATTENTE, 1, 2025-06-16 23:24:28, 2025-06-16 23:24:28)) at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#71 {main}

[previous exception] [object] (PDOException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `me...', Array)
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#72 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#73 {main}
"} 
[2025-06-16 23:43:27] local.ERROR: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (photo_avec_piece, merchant-documents/3hIdLq6CDm2PTvi6ptnllOwHkPReIfgtWdcD21FH.pdf, CV-En (2).pdf, application/pdf, 148651, EN_ATTENTE, 1, 2025-06-16 23:43:27, 2025-06-16 23:43:27)) {"userId":14,"exception":"[object] (Illuminate\\Database\\QueryException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (photo_avec_piece, merchant-documents/3hIdLq6CDm2PTvi6ptnllOwHkPReIfgtWdcD21FH.pdf, CV-En (2).pdf, application/pdf, 148651, EN_ATTENTE, 1, 2025-06-16 23:43:27, 2025-06-16 23:43:27)) at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#71 {main}

[previous exception] [object] (PDOException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `me...', Array)
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#72 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#73 {main}
"} 
[2025-06-16 23:44:17] local.ERROR: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (registre_commerce, merchant-documents/gPp2j3HttIEh3nJvRuCGBHYBc3v6lzeTaEZJLTcl.pdf, CV-En (2).pdf, application/pdf, 148651, EN_ATTENTE, 1, 2025-06-16 23:44:17, 2025-06-16 23:44:17)) {"userId":14,"exception":"[object] (Illuminate\\Database\\QueryException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 (Connection: mysql, SQL: insert into `merchant_validation_documents` (`document_type`, `file_path`, `original_name`, `mime_type`, `file_size`, `status`, `validation_id`, `updated_at`, `created_at`) values (registre_commerce, merchant-documents/gPp2j3HttIEh3nJvRuCGBHYBc3v6lzeTaEZJLTcl.pdf, CV-En (2).pdf, application/pdf, 148651, EN_ATTENTE, 1, 2025-06-16 23:44:17, 2025-06-16 23:44:17)) at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#71 {main}

[previous exception] [object] (PDOException(code: 01000): SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 at D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `me...', Array)
#2 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `me...', Array, Object(Closure))
#3 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `me...', Array, Object(Closure))
#4 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `me...', Array, 'id')
#5 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `me...', Array, 'id')
#6 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(315): Illuminate\\Database\\Eloquent\\Model->save()
#11 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Controllers\\MerchantValidationController.php(395): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->save(Object(App\\Models\\MerchantValidationDocument))
#12 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\MerchantValidationController->uploadDocument(Object(Illuminate\\Http\\Request))
#13 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MerchantValidationController), 'uploadDocument')
#14 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#17 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\MerchantValidationMiddleware.php(89): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#18 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\MerchantValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#47 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#48 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#72 D:\\Projet\\Lorelei_ecom\\admin_marchand_lorrelei\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Projet\\\\Lorel...')
#73 {main}
"} 
