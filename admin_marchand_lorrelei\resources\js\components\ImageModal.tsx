import React from 'react';
import { XMarkIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

interface ImageModalProps {
    isOpen: boolean;
    onClose: () => void;
    imageUrl: string;
    imageName: string;
    imageSize?: string;
}

export default function ImageModal({ isOpen, onClose, imageUrl, imageName, imageSize }: ImageModalProps) {
    if (!isOpen) return null;

    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = imageName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleBackdropClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    return (
        <div
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999] p-4"
            onClick={handleBackdropClick}
            style={{ zIndex: 9999 }}
        >
            <div className="relative max-w-4xl max-h-full bg-white dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                            {imageName}
                        </h3>
                        {imageSize && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                Taille : {imageSize}
                            </p>
                        )}
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                        <button
                            onClick={handleDownload}
                            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 
                                     hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"
                            title="Télécharger l'image"
                        >
                            <ArrowDownTrayIcon className="w-5 h-5" />
                        </button>
                        
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 
                                     hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"
                            title="Fermer"
                        >
                            <XMarkIcon className="w-5 h-5" />
                        </button>
                    </div>
                </div>

                {/* Image */}
                <div className="p-4 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                    <img
                        src={imageUrl}
                        alt={imageName}
                        className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg cursor-pointer"
                        onClick={onClose}
                        onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/images/image-error.png'; // Image de fallback
                        }}
                    />
                </div>

                {/* Footer */}
                <div className="p-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>Cliquez sur l'image ou appuyez sur Échap pour fermer</span>
                        <button
                            onClick={handleDownload}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 
                                     font-medium cursor-pointer transition-colors"
                        >
                            Télécharger
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Hook pour gérer la fermeture avec Échap
export function useImageModal() {
    const [isOpen, setIsOpen] = React.useState(false);
    const [imageData, setImageData] = React.useState<{
        url: string;
        name: string;
        size?: string;
    } | null>(null);

    const openModal = (url: string, name: string, size?: string) => {
        setImageData({ url, name, size });
        setIsOpen(true);
    };

    const closeModal = () => {
        setIsOpen(false);
        setImageData(null);
    };

    // Fermer avec Échap
    React.useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isOpen) {
                closeModal();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'hidden'; // Empêcher le scroll
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    return {
        isOpen,
        imageData,
        openModal,
        closeModal
    };
}
