/* Scrollbar personnalisée pour les zones de chat */

/* Webkit browsers (Chrome, Safari, Edge) */
.scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) transparent;
}

.dark .scrollbar-thin {
    scrollbar-color: rgb(75 85 99) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 3px;
    border: none;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(156 163 175);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
}

/* Scrollbar pour les zones de messages */
.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
}

/* Animation smooth pour le scroll */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Styles pour les bulles de chat */
.chat-bubble {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Styles pour la zone d'upload */
.upload-zone {
    transition: all 0.2s ease-in-out;
}

.upload-zone:hover {
    transform: scale(1.02);
}

/* Styles pour les fichiers joints */
.file-attachment {
    transition: all 0.2s ease-in-out;
}

.file-attachment:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive scrollbar */
@media (max-width: 768px) {
    .scrollbar-thin::-webkit-scrollbar {
        width: 4px;
    }
}

/* Focus states pour l'accessibilité */
.chat-input:focus {
    outline: none;
    ring: 2px;
    ring-color: rgb(59 130 246);
    border-color: rgb(59 130 246);
}

/* Animation pour les nouveaux messages */
.new-message {
    animation: newMessageSlide 0.4s ease-out;
}

@keyframes newMessageSlide {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Styles pour l'indicateur de connexion */
.connection-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Styles pour les badges de statut */
.status-badge {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Optimisation pour les écrans haute résolution */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .scrollbar-thin::-webkit-scrollbar {
        width: 8px;
    }
    
    .scrollbar-thin::-webkit-scrollbar-thumb {
        border-radius: 4px;
    }
}
