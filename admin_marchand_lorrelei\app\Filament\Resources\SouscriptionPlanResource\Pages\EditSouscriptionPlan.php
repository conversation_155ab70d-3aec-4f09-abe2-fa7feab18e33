<?php

namespace App\Filament\Resources\SouscriptionPlanResource\Pages;

use App\Filament\Resources\SouscriptionPlanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSouscriptionPlan extends EditRecord
{
    protected static string $resource = SouscriptionPlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('Supprimer le plan'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Plan modifié avec succès';
    }
} 