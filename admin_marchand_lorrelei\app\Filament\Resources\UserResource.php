<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Model;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Gestion des Utilisateurs';

    protected static ?int $navigationSort = 1;

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->role === 'super_admin';
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->role === 'super_admin';
    }

    public static function form(Form $form): Form
    {
        $isCreating = $form->getOperation() === 'create';
        $isSuperAdmin = auth()->user()->role === 'super_admin';

        return $form
            ->schema([
                Forms\Components\Section::make('Informations de base')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nom')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
                            ->dehydrated(fn (?string $state): bool => filled($state))
                            ->required(fn (string $operation): bool => $operation === 'create'),

                        Forms\Components\Select::make('role')
                            ->options([
                                'Client' => 'Client',
                                'Marchand' => 'Marchand',
                                'Admin' => 'Admin',
                                'super_admin' => 'Super Admin',
                            ])
                            ->disabled(!$isSuperAdmin)
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Statut du compte')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Compte actif')
                            ->default(true)
                            ->disabled(!$isSuperAdmin),

                        Forms\Components\Toggle::make('is_admin')
                            ->label('Administrateur')
                            ->default(false)
                            ->disabled(!$isSuperAdmin),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('Email vérifié le')
                            ->disabled(),

                        Forms\Components\DateTimePicker::make('last_login_at')
                            ->label('Dernière connexion')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Jetons de sécurité')
                    ->schema([
                        Forms\Components\TextInput::make('email_verification_token')
                            ->label('Jeton de vérification email')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('password_reset_token')
                            ->label('Jeton de réinitialisation')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\DateTimePicker::make('password_reset_expires_at')
                            ->label('Expiration du jeton')
                            ->disabled(),
                    ])->columns(2)
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        $isSuperAdmin = auth()->user()->role === 'super_admin';

        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('role')
                    ->label('Rôle')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'super_admin' => 'purple',
                        'Admin' => 'danger',
                        'Marchand' => 'warning',
                        'Client' => 'success',
                        default => 'gray',
                    }),

                IconColumn::make('is_admin')
                    ->label('Admin')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('email_verified_at')
                    ->label('Email vérifié')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('last_login_at')
                    ->label('Dernière connexion')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('role')
                    ->options([
                        'super_admin' => 'Super Admin',
                        'Admin' => 'Admin',
                        'Marchand' => 'Marchand',
                        'Client' => 'Client',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif'),
                Tables\Filters\TernaryFilter::make('is_admin')
                    ->label('Admin'),
                Tables\Filters\TernaryFilter::make('email_verified_at')
                    ->label('Email vérifié')
                    ->nullable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible($isSuperAdmin),
                Tables\Actions\DeleteAction::make()
                    ->visible($isSuperAdmin),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible($isSuperAdmin),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\AdressesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
