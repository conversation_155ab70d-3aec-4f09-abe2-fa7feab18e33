<?php

namespace App\Filament\Marchand\Resources\ImportResource\Pages;

use App\Filament\Marchand\Resources\ImportResource;
use App\Imports\ProduitImport;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ImportIndex extends Page
{
    use InteractsWithForms;

    protected static string $resource = ImportResource::class;

    protected static string $view = 'filament.marchand.resources.import-resource.pages.import-index';

    protected static ?string $title = 'Importation de produits';

    public ?array $data = [];

    // Propriété publique requise par Livewire
    public $products_file = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function hydrate(): void
    {
        // S'assurer que les propriétés sont correctement initialisées
        if (!is_array($this->products_file)) {
            $this->products_file = [];
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Importation de produits')
                    ->description('Importez vos produits à partir d\'un fichier CSV ou Excel')
                    ->schema([
                        FileUpload::make('products_file')
                            ->label('Fichier CSV/Excel')
                            ->acceptedFileTypes(['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
                            ->maxSize(10240) // 10MB
                            ->directory('temp/imports')
                            ->visibility('private')
                            ->live()
                            ->required(),
                    ]),
            ]);
    }

    public function importProducts()
    {
        // Récupérer les données du formulaire
        $data = $this->form->getState();

        if (empty($data['products_file'])) {
            Notification::make()
                ->title('Erreur')
                ->body('Veuillez sélectionner un fichier à importer')
                ->danger()
                ->send();
            return;
        }

        $filePath = $this->getFilePath($data['products_file']);

        // Récupérer l'ID du marchand connecté
        $marchandId = null;
        if (Auth::check() && Auth::user()->marchand) {
            $marchandId = Auth::user()->marchand->id;
        }

        if (!$marchandId) {
            Notification::make()
                ->title('Erreur')
                ->body('Vous devez être connecté en tant que marchand pour importer des produits')
                ->danger()
                ->send();
            return;
        }

        $import = new ProduitImport($marchandId);

        try {
            $import->import($filePath);
            $results = $import->getResults();

            // Supprimer le fichier temporaire
            $this->cleanupTempFile($data['products_file']);

            // Afficher les résultats
            $this->showImportResults($results);

            // Réinitialiser la propriété publique
            $this->products_file = [];
            $this->form->fill([
                'products_file' => null,
            ]);
        } catch (\Exception $e) {
            Notification::make()
                ->title('Erreur')
                ->body('Erreur lors de l\'importation des produits: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Obtient le chemin complet du fichier téléchargé
     */
    protected function getFilePath($fileKey)
    {
        try {
            // Créer le répertoire d'importation s'il n'existe pas
            $importDir = storage_path('app/imports');
            if (!is_dir($importDir)) {
                mkdir($importDir, 0755, true);
            }

            // Nom de fichier unique pour éviter les collisions
            $uniqueFilename = uniqid('import_') . '_' . basename($fileKey);
            $newPath = $importDir . '/' . $uniqueFilename;

            // Essayer différents chemins possibles pour le fichier source
            $possiblePaths = [
                Storage::disk('local')->path('public/' . $fileKey),
                storage_path('app/livewire-tmp/' . $fileKey),
                storage_path('app/livewire-tmp/' . basename($fileKey)),
                storage_path('app/public/' . $fileKey),
                public_path('storage/' . $fileKey),
            ];

            foreach ($possiblePaths as $sourcePath) {
                if (file_exists($sourcePath) && is_readable($sourcePath)) {
                    // Copier le fichier vers notre emplacement sécurisé
                    if (copy($sourcePath, $newPath)) {
                        return $newPath;
                    }
                }
            }

            // Si le fichier est une URL encodée en base64 (cas de certains fichiers Livewire)
            if (strpos($fileKey, '-meta') !== false) {
                // Extraire le nom de fichier original
                $parts = explode('-meta', $fileKey);
                $encodedName = $parts[0];

                // Chercher dans le répertoire livewire-tmp pour des fichiers correspondants
                $livewireTmpDir = storage_path('app/livewire-tmp');
                if (is_dir($livewireTmpDir)) {
                    $files = scandir($livewireTmpDir);
                    foreach ($files as $file) {
                        if (strpos($file, $encodedName) === 0) {
                            $sourcePath = $livewireTmpDir . '/' . $file;
                            if (file_exists($sourcePath) && is_readable($sourcePath)) {
                                if (copy($sourcePath, $newPath)) {
                                    return $newPath;
                                }
                            }
                        }
                    }
                }
            }

            // Si nous arrivons ici, nous n'avons pas pu trouver ou copier le fichier
            throw new \Exception("Impossible de trouver ou d'accéder au fichier téléchargé. Veuillez réessayer.");
        } catch (\Exception $e) {
            throw new \Exception("Erreur lors de la récupération du fichier: " . $e->getMessage());
        }
    }

    /**
     * Nettoie le fichier temporaire après utilisation
     */
    protected function cleanupTempFile($fileKey)
    {
        try {
            // Supprimer le fichier du disque local
            if (!empty($fileKey)) {
                // Chemins possibles pour les fichiers temporaires
                $possiblePaths = [
                    Storage::disk('local')->path('public/' . $fileKey),
                    storage_path('app/livewire-tmp/' . $fileKey),
                    storage_path('app/livewire-tmp/' . basename($fileKey)),
                    storage_path('app/public/' . $fileKey),
                    public_path('storage/' . $fileKey),
                ];

                // Supprimer tous les fichiers temporaires possibles
                foreach ($possiblePaths as $path) {
                    if (file_exists($path)) {
                        @unlink($path);
                    }
                }

                // Supprimer les fichiers dans le répertoire imports
                $importDir = storage_path('app/imports');
                if (is_dir($importDir)) {
                    $files = scandir($importDir);
                    foreach ($files as $file) {
                        if ($file !== '.' && $file !== '..' && strpos($file, basename($fileKey)) !== false) {
                            @unlink($importDir . '/' . $file);
                        }
                    }
                }

                // Si le fichier est une URL encodée en base64 (cas de certains fichiers Livewire)
                if (strpos($fileKey, '-meta') !== false) {
                    $parts = explode('-meta', $fileKey);
                    $encodedName = $parts[0];

                    // Chercher et supprimer les fichiers correspondants dans livewire-tmp
                    $livewireTmpDir = storage_path('app/livewire-tmp');
                    if (is_dir($livewireTmpDir)) {
                        $files = scandir($livewireTmpDir);
                        foreach ($files as $file) {
                            if (strpos($file, $encodedName) === 0) {
                                @unlink($livewireTmpDir . '/' . $file);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Journaliser l'erreur mais ne pas interrompre le processus
            \Illuminate\Support\Facades\Log::error("Erreur lors de la suppression du fichier temporaire: " . $e->getMessage());
        }
    }

    protected function showImportResults(array $results)
    {
        $notification = Notification::make()
            ->title('Importation terminée')
            ->body("Importation des produits terminée avec succès.\n" .
                   "Total: {$results['total']}\n" .
                   "Créés: {$results['created']}\n" .
                   "Mis à jour: {$results['updated']}");

        if (!empty($results['errors'])) {
            $errorCount = count($results['errors']);
            $errorMessage = "Erreurs: {$errorCount}";

            if ($errorCount > 0) {
                $errorMessage .= "\n" . implode("\n", array_slice($results['errors'], 0, 5));

                if ($errorCount > 5) {
                    $errorMessage .= "\n... et " . ($errorCount - 5) . " autres erreurs";
                }
            }

            $notification->warning();
            $notification->body($notification->getBody() . "\n\n" . $errorMessage);
        } else {
            $notification->success();
        }

        $notification->send();
    }

    public function downloadProductsTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="products_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'nom_fr', 'nom_en', 'description_fr', 'description_en', 'categorie',
                'prix', 'currency', 'stock', 'prix_remise', 'date_debut_remise', 'date_fin_remise',
                'poids', 'longueur', 'largeur', 'hauteur', 'product_code', 'marque'
            ]);

            // Exemples de données avec traductions
            fputcsv($file, [
                'Ordinateur portable XYZ', 'XYZ Laptop', 'Ordinateur portable haute performance avec écran 15.6"', 'High-performance laptop with 15.6" screen', 'Ordinateurs',
                '999.99', 'FCFA', '10', '899.99', '2023-01-01', '2023-12-31',
                '2.5', '35', '25', '2', 'XYZ123456789', 'Dell'
            ]);

            fputcsv($file, [
                'Smartphone ABC', 'ABC Smartphone', 'Smartphone avec écran AMOLED et appareil photo 48MP', 'Smartphone with AMOLED display and 48MP camera', 'Smartphones',
                '599.99', 'EUR', '20', '', '', '',
                '0.2', '15', '7', '1', 'ABC987654321', 'Samsung'
            ]);

            fputcsv($file, [
                'Casque sans fil Pro', 'Pro Wireless Headphones', 'Casque sans fil avec réduction de bruit active', 'Wireless headphones with active noise cancellation', 'Accessoires',
                '199.99', 'USD', '15', '149.99', '2023-02-01', '2023-03-31',
                '0.3', '18', '16', '8', 'PRO456789123', 'Sony'
            ]);

            fputcsv($file, [
                'Montre connectée', 'Smartwatch', 'Montre connectée avec suivi de la santé et notifications', 'Smartwatch with health tracking and notifications', 'Accessoires',
                '249.99', 'XAF', '8', '', '', '',
                '0.05', '4.5', '4.5', '1.2', 'SW789123456', 'Apple'
            ]);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
