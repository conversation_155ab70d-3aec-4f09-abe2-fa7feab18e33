<?php

namespace App\Notifications;

use App\Models\MerchantValidation;
use App\Models\Marchand;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MerchantApprovedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public MerchantValidation $validation;
    public ?Marchand $marchand;

    /**
     * Create a new notification instance.
     */
    public function __construct(MerchantValidation $validation, ?Marchand $marchand = null)
    {
        $this->validation = $validation;
        $this->marchand = $marchand;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $businessInfo = $this->validation->business_info ?? [];
        $businessName = $businessInfo['nomEntreprise'] ?? $this->marchand?->nomEntreprise ?? 'votre boutique';

        return (new MailMessage)
            ->subject('🎉 Félicitations ! Votre boutique a été approuvée - Lorrelei')
            ->view('emails.merchant.approved', [
                'user_name' => $notifiable->name,
                'business_name' => $businessName,
                'dashboard_url' => route('filament.marchand.pages.dashboard'),
                'validation' => $this->validation,
                'marchand' => $this->marchand,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $businessInfo = $this->validation->business_info ?? [];
        
        return [
            'type' => 'merchant_approved',
            'title' => 'Boutique approuvée !',
            'message' => 'Félicitations ! Votre boutique a été approuvée et est maintenant active.',
            'validation_id' => $this->validation->id,
            'marchand_id' => $this->marchand?->id,
            'business_name' => $businessInfo['nomEntreprise'] ?? $this->marchand?->nomEntreprise ?? 'Votre boutique',
            'approved_at' => $this->validation->validated_at?->toISOString(),
            'action_url' => route('filament.marchand.pages.dashboard'),
        ];
    }
}
