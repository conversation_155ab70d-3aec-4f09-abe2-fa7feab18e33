import { useState, useEffect } from 'react';
import { X, Download, ZoomIn, ZoomOut, RotateCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ImageModalProps {
    isOpen: boolean;
    onClose: () => void;
    imageUrl: string;
    imageName: string;
    imageSize?: number;
    downloadUrl?: string;
}

export function ImageModal({ 
    isOpen, 
    onClose, 
    imageUrl, 
    imageName, 
    imageSize,
    downloadUrl 
}: ImageModalProps) {
    const [zoom, setZoom] = useState(1);
    const [rotation, setRotation] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    // Reset zoom et rotation quand on ouvre une nouvelle image
    useEffect(() => {
        if (isOpen) {
            setZoom(1);
            setRotation(0);
            setIsLoading(true);
        }
    }, [isOpen, imageUrl]);

    // Fermer avec Escape
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') onClose();
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
    const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));
    const handleRotate = () => setRotation(prev => (prev + 90) % 360);

    const handleDownload = () => {
        if (downloadUrl) {
            // Utiliser la route de téléchargement sécurisée
            window.open(downloadUrl, '_blank');
        } else {
            // Fallback : téléchargement direct
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = imageName;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const formatFileSize = (bytes?: number) => {
        if (!bytes) return '';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    };

    const isImage = imageName.match(/\.(jpg|jpeg|png|gif|webp)$/i);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay */}
            <div 
                className="absolute inset-0 bg-black/80 backdrop-blur-sm"
                onClick={onClose}
            />

            {/* Modal */}
            <div className="relative z-10 max-w-[95vw] max-h-[95vh] bg-white dark:bg-gray-900 rounded-lg shadow-2xl overflow-hidden">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                            {imageName}
                        </h3>
                        {imageSize && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                {formatFileSize(imageSize)}
                            </p>
                        )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                        {isImage && (
                            <>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleZoomOut}
                                    disabled={zoom <= 0.25}
                                    className="h-8 w-8 p-0"
                                >
                                    <ZoomOut className="h-4 w-4" />
                                </Button>
                                
                                <span className="text-sm text-gray-500 dark:text-gray-400 min-w-[3rem] text-center">
                                    {Math.round(zoom * 100)}%
                                </span>
                                
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleZoomIn}
                                    disabled={zoom >= 3}
                                    className="h-8 w-8 p-0"
                                >
                                    <ZoomIn className="h-4 w-4" />
                                </Button>

                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleRotate}
                                    className="h-8 w-8 p-0"
                                >
                                    <RotateCw className="h-4 w-4" />
                                </Button>
                            </>
                        )}

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleDownload}
                            className="h-8 w-8 p-0"
                        >
                            <Download className="h-4 w-4" />
                        </Button>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="h-8 w-8 p-0"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Content */}
                <div className="relative bg-gray-100 dark:bg-gray-800 flex items-center justify-center min-h-[400px] max-h-[70vh] overflow-auto">
                    {isImage ? (
                        <div className="relative">
                            {isLoading && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                </div>
                            )}
                            <img
                                src={imageUrl}
                                alt={imageName}
                                className="max-w-none transition-all duration-200 ease-in-out"
                                style={{
                                    transform: `scale(${zoom}) rotate(${rotation}deg)`,
                                    maxWidth: isLoading ? '0' : 'none',
                                    maxHeight: isLoading ? '0' : '70vh'
                                }}
                                onLoad={() => setIsLoading(false)}
                                onError={() => setIsLoading(false)}
                            />
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center p-8 text-center">
                            <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-lg flex items-center justify-center mb-4">
                                <span className="text-2xl">📄</span>
                            </div>
                            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                Aperçu non disponible
                            </h4>
                            <p className="text-gray-500 dark:text-gray-400 mb-4">
                                Ce type de fichier ne peut pas être affiché en aperçu
                            </p>
                            <Button onClick={handleDownload} className="flex items-center gap-2">
                                <Download className="h-4 w-4" />
                                Télécharger le fichier
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
