<?php

namespace App\Filament\Widgets;

use App\Models\Commande;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class SalesChart extends ChartWidget
{
    protected static ?string $heading = 'Ventes des 30 derniers jours';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = Commande::where('creeLe', '>=', now()->subDays(30))
            ->whereIn('statut', ['EnAttente', 'EnCoursDeTraitement', 'Expédié', 'Livré'])
            ->select(DB::raw('DATE(creeLe) as date'), DB::raw('SUM(montantTotal) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $dates = [];
        $totals = [];

        // Créer un tableau avec tous les jours des 30 derniers jours
        for ($i = 30; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dates[] = now()->subDays($i)->format('d/m');

            $dayData = $data->firstWhere('date', $date);
            $totals[] = $dayData ? $dayData->total : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Ventes (€)',
                    'data' => $totals,
                    'fill' => 'start',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                ],
            ],
            'labels' => $dates,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
