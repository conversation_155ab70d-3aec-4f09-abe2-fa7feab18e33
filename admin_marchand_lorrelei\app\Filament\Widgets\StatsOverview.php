<?php

namespace App\Filament\Widgets;

use App\Models\Commande;
use App\Models\Produit;
use App\Models\User;
use App\Models\Marchand;
use App\Models\MerchantValidation;
use App\Services\DashboardStatsService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $dashboardService = new DashboardStatsService();
        $stats = $dashboardService->getStatistiquesAdmin();

        // Statistiques des utilisateurs
        $totalUsers = User::count();
        $totalClients = User::where('role', 'Client')->count();

        // Statistiques des produits
        $totalProduits = Produit::count();
        $produitsEnRupture = Produit::where('stock', 0)->count();

        return [
            Stat::make('Utilisateurs', $totalUsers)
                ->description($totalClients . ' clients, ' . $stats['marchandsActifs'] . ' marchands actifs')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Commandes principales', $stats['commandesPrincipalesMois'])
                ->description('Nouveau système multi-marchands')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('success'),

            Stat::make('Revenus totaux', number_format($stats['revenusTotauxMois'], 0, ',', ' ') . ' FCFA')
                ->description('Ce mois-ci')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Commissions plateforme', number_format($stats['commissionsPlateformeMois'], 0, ',', ' ') . ' FCFA')
                ->description('Ce mois-ci')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('info'),

            Stat::make('Marchands en attente', $stats['marchandsEnAttente'])
                ->description('À valider')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Versements en cours', $stats['versementsEnCours'])
                ->description(number_format($stats['montantVersementsEnCours'], 0, ',', ' ') . ' FCFA')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('primary'),

            Stat::make('Produits', $totalProduits)
                ->description($produitsEnRupture . ' en rupture de stock')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color($produitsEnRupture > 0 ? 'danger' : 'success'),
        ];
    }
}
