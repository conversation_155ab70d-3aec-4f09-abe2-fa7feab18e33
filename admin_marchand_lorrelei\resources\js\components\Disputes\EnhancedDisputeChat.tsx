import React, { useState, useEffect, useRef } from 'react';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface DisputeMessage {
    id: number;
    message: string;
    auteur_type: string;
    auteur_nom: string;
    type_message: string;
    created_at: string;
    pieces_jointes?:  Array<{
        nom_original: string;
        nom_fichier: string;
        chemin: string;
        taille: number;
        type_mime: string;
    }>;
    interne?: boolean;
}

interface EnhancedDisputeChatProps {
    disputeId: string;
    messages: DisputeMessage[];
    currentUserId: string;
    currentUserType: 'admin' | 'marchand';
    onSendMessage: (message: string, type: string, files?: File[]) => Promise<void>;
    onImageClick?: (imageData: any) => void;
    className?: string;
}

export const EnhancedDisputeChat: React.FC<EnhancedDisputeChatProps> = ({
    disputeId,
    messages: initialMessages,
    currentUserId,
    currentUserType,
    onSendMessage,
    onImageClick,
    className = '',
}) => {
    const [messages, setMessages] = useState<DisputeMessage[]>(initialMessages);
    const [newMessage, setNewMessage] = useState('');
    const [messageType, setMessageType] = useState<'message' | 'resolution' | 'escalation'>('message');
    const [sending, setSending] = useState(false);
    const [attachments, setAttachments] = useState<File[]>([]);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Hook WebSocket avec gestion automatique de la frappe
    const {
        typingUsers,
        formatTypingUsers,
        useTypingInput,
    } = useReverbWebSocket({
        disputeId,
        onMessage: (data) => {
            console.log('🔥 [ADMIN] Message WebSocket reçu:', data);

            // Vérifier la structure des données
            if (!data.message) {
                console.error('❌ [ADMIN] Pas de propriété message dans les données:', data);
                return;
            }

            // Ajouter le nouveau message à la liste
            const newMsg: DisputeMessage = {
                id: parseInt(data.message.id),
                message: data.message.message,
                auteur_type: data.message.auteur_type,
                auteur_nom: data.message.auteur_nom,
                type_message: data.message.type_message,
                created_at: data.message.created_at,
                pieces_jointes: data.message.pieces_jointes,
                interne: data.message.interne,
            };

            console.log('✅ [ADMIN] Nouveau message créé:', newMsg);
            setMessages(prev => {
                const updated = [...prev, newMsg];
                console.log('📝 [ADMIN] Messages mis à jour:', updated);
                return updated;
            });
            scrollToBottom();
        },
    });

    // Hook pour gérer la frappe automatiquement
    const { handleInputChange, handleInputBlur } = useTypingInput();

    // Synchroniser les messages avec les props
    useEffect(() => {
        setMessages(initialMessages);
    }, [initialMessages]);

    // Faire défiler vers le bas automatiquement
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Gérer les changements dans l'input
    const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setNewMessage(value);
        handleInputChange(value);
    };

    // Gérer l'envoi du message
    const handleSendMessage = async () => {
        if (!newMessage.trim() && attachments.length === 0) return;

        setSending(true);
        try {
            await onSendMessage(newMessage, messageType, attachments);
            setNewMessage('');
            setAttachments([]);
            setMessageType('message');
            handleInputBlur(); // Arrêter l'indicateur de frappe
        } catch (error) {
            console.error('Erreur envoi message litige:', error);
        } finally {
            setSending(false);
        }
    };

    // Gérer l'appui sur Entrée
    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Gérer la sélection de fichiers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setAttachments(prev => [...prev, ...files]);
    };

    // Supprimer un fichier sélectionné
    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    // Formater la date
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 1) {
            return 'À l\'instant';
        } else if (diffInHours < 24) {
            return `Il y a ${Math.floor(diffInHours)}h`;
        } else {
            return date.toLocaleDateString('fr-FR', {
                day: 'numeric',
                month: 'short',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    };

    // Obtenir la couleur du type de message
    const getMessageTypeColor = (type: string) => {
        switch (type) {
            case 'resolution': return 'border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20';
            case 'escalation': return 'border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20';
            default: return '';
        }
    };

    // Filtrer les utilisateurs qui tapent (exclure l'utilisateur actuel)
    const otherTypingUsers = typingUsers.filter(user => user.user_id.toString() !== currentUserId);

    return (
        <div className={`flex flex-col h-full ${className}`}>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`flex ${message.auteur_type === currentUserType ? 'justify-end' : 'justify-start'}`}
                    >
                        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.auteur_type === currentUserType
                                ? 'bg-blue-500 text-white'
                                : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                        } ${getMessageTypeColor(message.type_message)}`}>

                            {message.auteur_type !== currentUserType && (
                                <div className="text-sm font-medium mb-1 opacity-75">
                                    {message.auteur_nom}
                                    {message.interne && (
                                        <span className="ml-2 text-xs bg-yellow-200 text-yellow-800 px-1 rounded">
                                            Interne
                                        </span>
                                    )}
                                </div>
                            )}

                            {message.type_message !== 'message' && (
                                <div className="text-xs font-medium mb-1 opacity-75">
                                    {message.type_message === 'resolution' ? '✅ Résolution' : '⚠️ Escalade'}
                                </div>
                            )}

                            <div className="text-sm whitespace-pre-wrap">
                                {message.message}
                            </div>

                            {/* Affichage des pièces jointes */}
                            {message.pieces_jointes && message.pieces_jointes.length > 0 && (
                                <div className="mt-2 space-y-1">
                                    {message.pieces_jointes.map((file, index) => (
                                        <div key={index} className="text-sm opacity-75 cursor-pointer hover:underline"
                                             onClick={() => onImageClick?.({ url: file.chemin, name: file.nom_original })}>
                                            📎 {file.nom_original}
                                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                                <img src={file.chemin} alt={file.nom_original} className="w-4 h-4" />
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            )}

                            <div className={`text-xs mt-1 ${
                                message.auteur_type === currentUserType ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                            }`}>
                                {formatDate(message.created_at)}
                            </div>
                        </div>
                    </div>
                ))}

                {/* Indicateur "en train d'écrire" */}
                {otherTypingUsers.length > 0 && (
                    <div className="flex justify-start">
                        <div className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-4 py-2 rounded-lg text-sm animate-pulse">
                            <div className="flex items-center space-x-2">
                                <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span>{formatTypingUsers(otherTypingUsers)}</span>
                            </div>
                        </div>
                    </div>
                )}

                <div ref={messagesEndRef} />
            </div>

            {/* Zone d'envoi de message fixe */}
            <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                {/* Sélecteur de type de message pour admin */}
                {currentUserType === 'admin' && (
                    <div className="mb-3">
                        <select
                            value={messageType}
                            onChange={(e) => setMessageType(e.target.value as any)}
                            className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        >
                            <option value="message">Message normal</option>
                            <option value="resolution">Proposition de résolution</option>
                            <option value="escalation">Escalade</option>
                        </select>
                    </div>
                )}

                {/* Pièces jointes sélectionnées */}
                {attachments.length > 0 && (
                    <div className="mb-3 flex flex-wrap gap-2">
                        {attachments.map((file, index) => (
                            <div key={index} className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-1">
                                <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-32">
                                    {file.name}
                                </span>
                                <button
                                    onClick={() => removeAttachment(index)}
                                    className="ml-2 text-gray-500 hover:text-red-500"
                                >
                                    ×
                                </button>
                            </div>
                        ))}
                    </div>
                )}

                <div className="flex items-end space-x-2">
                    <div className="flex-1">
                        <textarea
                            placeholder="Tapez votre message..."
                            value={newMessage}
                            onChange={handleMessageChange}
                            onKeyDown={handleKeyPress}
                            onBlur={handleInputBlur}
                            className="w-full min-h-[60px] resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            disabled={sending}
                        />
                    </div>

                    <div className="flex flex-col space-y-2">
                        <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileSelect}
                            multiple
                            className="hidden"
                            accept="image/*,.pdf,.doc,.docx,.txt"
                        />

                        <button
                            onClick={() => fileInputRef.current?.click()}
                            disabled={sending}
                            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg"
                            title="Joindre un fichier"
                        >
                            📎
                        </button>

                        <button
                            onClick={handleSendMessage}
                            disabled={sending || (!newMessage.trim() && attachments.length === 0)}
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {sending ? '...' : 'Envoyer'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
