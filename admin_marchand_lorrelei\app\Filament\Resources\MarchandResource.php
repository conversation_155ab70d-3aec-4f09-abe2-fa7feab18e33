<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MarchandResource\Pages;
use App\Filament\Resources\MarchandResource\RelationManagers;
use App\Models\Marchand;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Collection;

class MarchandResource extends Resource
{
    protected static ?string $model = Marchand::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationLabel = 'Marchands';

    protected static ?string $modelLabel = 'Marchand';

    protected static ?string $pluralModelLabel = 'Marchands';

    protected static ?string $navigationGroup = 'Gestion des marchands';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations du compte')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Utilisateur')
                            ->options(
                                User::where('role', 'Marchand')
                                    ->orWhereDoesntHave('marchand')
                                    ->whereNotNull('email')
                                    ->where('email', '!=', '')
                                    ->pluck('email', 'id')
                            )
                            ->searchable()
                            ->required()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Nom')
                                    ->required(),
                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->unique('users', 'email'),
                                Forms\Components\TextInput::make('password')
                                    ->label('Mot de passe')
                                    ->password()
                                    ->required()
                                    ->confirmed(),
                                Forms\Components\TextInput::make('password_confirmation')
                                    ->label('Confirmation du mot de passe')
                                    ->password()
                                    ->required(),
                                Forms\Components\Hidden::make('role')
                                    ->default('Marchand'),
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Actif')
                                    ->default(true),
                            ])
                            ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                                return $action
                                    ->modalHeading('Créer un nouvel utilisateur')
                                    ->modalWidth('lg');
                            }),
                        Forms\Components\TextInput::make('nomEntreprise')
                            ->label('Nom de l\'entreprise')
                            ->required()
                            ->maxLength(255),
                    ]),
                Forms\Components\Section::make('Informations fiscales et bancaires')
                    ->schema([
                        Forms\Components\TextInput::make('idFiscal')
                            ->label('Identifiant fiscal')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('banqueNom')
                            ->label('Nom de la banque')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('banqueNumeroCompte')
                            ->label('Numéro de compte bancaire')
                            ->maxLength(50),
                    ]),
                Forms\Components\Section::make('Adresse')
                    ->schema([
                        Forms\Components\Select::make('adresse_id')
                            ->label('Adresse')
                            ->relationship('adresse', 'rue')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('rue')
                                    ->label('Rue')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('ville')
                                    ->label('Ville')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('etat')
                                    ->label('État/Province')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('pays')
                                    ->label('Pays')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('codePostal')
                                    ->label('Code postal')
                                    ->required()
                                    ->maxLength(20),
                                Forms\Components\Hidden::make('type')
                                    ->default('Entreprise'),
                            ])
                            ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                                return $action
                                    ->modalHeading('Créer une nouvelle adresse')
                                    ->modalWidth('lg');
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Nom de l\'entreprise')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('produits_count')
                    ->label('Nombre de produits')
                    ->counts('produits')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commandes_count')
                    ->label('Nombre de commandes')
                    ->counts('commandes')
                    ->sortable(),
                Tables\Columns\IconColumn::make('user.is_active')
                    ->label('Actif')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('Statut')
                    ->options([
                        '1' => 'Actif',
                        '0' => 'Inactif',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['value'] !== null,
                                fn (Builder $query): Builder => $query->whereHas(
                                    'user',
                                    fn (Builder $query): Builder => $query->where('is_active', $data['value'])
                                )
                            );
                    }),
                Tables\Filters\Filter::make('has_products')
                    ->label('Avec produits')
                    ->query(fn (Builder $query): Builder => $query->has('produits')),
                Tables\Filters\Filter::make('no_products')
                    ->label('Sans produits')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('produits')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (Marchand $record): string => $record->user->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (Marchand $record): string => $record->user->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (Marchand $record): string => $record->user->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->action(function (Marchand $record): void {
                        $user = $record->user;
                        $user->is_active = !$user->is_active;
                        $user->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $user = $record->user;
                                $user->is_active = true;
                                $user->save();
                            }
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $user = $record->user;
                                $user->is_active = false;
                                $user->save();
                            }
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informations du marchand')
                    ->schema([
                        Infolists\Components\TextEntry::make('nomEntreprise')
                            ->label('Nom de l\'entreprise')
                            ->weight(FontWeight::Bold)
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large),
                        Infolists\Components\TextEntry::make('user.email')
                            ->label('Email'),
                        Infolists\Components\TextEntry::make('user.is_active')
                            ->label('Statut')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'danger')
                            ->formatStateUsing(fn (bool $state): string => $state ? 'Actif' : 'Inactif'),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Inscrit depuis')
                            ->dateTime('d/m/Y'),
                    ])
                    ->columns(2),
                Infolists\Components\Section::make('Informations fiscales et bancaires')
                    ->schema([
                        Infolists\Components\TextEntry::make('idFiscal')
                            ->label('Identifiant fiscal'),
                        Infolists\Components\TextEntry::make('banqueNom')
                            ->label('Nom de la banque'),
                        Infolists\Components\TextEntry::make('banqueNumeroCompte')
                            ->label('Numéro de compte bancaire'),
                    ])
                    ->columns(3),
                Infolists\Components\Section::make('Adresse')
                    ->schema([
                        Infolists\Components\TextEntry::make('adresse.rue')
                            ->label('Rue'),
                        Infolists\Components\TextEntry::make('adresse.ville')
                            ->label('Ville'),
                        Infolists\Components\TextEntry::make('adresse.etat')
                            ->label('État/Province'),
                        Infolists\Components\TextEntry::make('adresse.pays')
                            ->label('Pays'),
                        Infolists\Components\TextEntry::make('adresse.codePostal')
                            ->label('Code postal'),
                    ])
                    ->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProduitsRelationManager::class,
            RelationManagers\CommandesRelationManager::class,
            RelationManagers\PaiementsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarchands::route('/'),
            'create' => Pages\CreateMarchand::route('/create'),
            'view' => Pages\ViewMarchand::route('/{record}'),
            'edit' => Pages\EditMarchand::route('/{record}/edit'),
        ];
    }
}
