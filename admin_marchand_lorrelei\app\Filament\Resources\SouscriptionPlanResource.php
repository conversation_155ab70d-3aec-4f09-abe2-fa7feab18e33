<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SouscriptionPlanResource\Pages;
use App\Models\SouscriptionPlan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SouscriptionPlanResource extends Resource
{
    protected static ?string $model = SouscriptionPlan::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Gestion Abonnements';

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('Plan d\'abonnement');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Plans d\'abonnement');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Plan')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Informations générales')
                            ->schema([
                                Forms\Components\Grid::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('nom.fr')
                                            ->label('Nom (Français)')
                                            ->required(),
                                        Forms\Components\TextInput::make('nom.en')
                                            ->label('Nom (Anglais)')
                                            ->required(),
                                        Forms\Components\Select::make('type')
                                            ->label('Type de plan')
                                            ->options(SouscriptionPlan::TYPES)
                                            ->required(),
                                        Forms\Components\Select::make('status')
                                            ->label('Statut')
                                            ->options(SouscriptionPlan::STATUSES)
                                            ->default('active')
                                            ->required(),
                                    ])->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('Tarification')
                            ->schema([
                                Forms\Components\Grid::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('current_price')
                                            ->label('Prix actuel (FCFA)')
                                            ->numeric()
                                            ->required(),
                                        Forms\Components\TextInput::make('discount_price')
                                            ->label('Prix réduit (FCFA)')
                                            ->numeric(),
                                        Forms\Components\TextInput::make('discount_percentage')
                                            ->label('Pourcentage de réduction')
                                            ->numeric()
                                            ->suffix('%')
                                            ->maxValue(100),
                                        Forms\Components\TextInput::make('commission_range')
                                            ->label('Fourchette de commission')
                                            ->placeholder('ex: 4-8%')
                                            ->required(),
                                    ])->columns(2),
                                Forms\Components\Grid::make()
                                    ->schema([
                                        Forms\Components\Toggle::make('is_annual')
                                            ->label('Plan annuel')
                                            ->default(false),
                                        Forms\Components\Select::make('periode')
                                            ->label('Période de facturation')
                                            ->options(SouscriptionPlan::PERIODES)
                                            ->default('mois')
                                            ->required(),
                                        Forms\Components\Toggle::make('popular')
                                            ->label('Plan populaire')
                                            ->default(false),
                                    ])->columns(3),
                            ]),
                        Forms\Components\Tabs\Tab::make('Fonctionnalités')
                            ->schema([
                                Forms\Components\Repeater::make('features.fr')
                                    ->label('Fonctionnalités (Français)')
                                    ->simple(true)
                                    ->schema([
                                        Forms\Components\TextInput::make('value')
                                            ->label('Fonctionnalité')
                                            ->required(),
                                    ])
                                    ->columnSpanFull(),
                                Forms\Components\Repeater::make('features.en')
                                    ->label('Fonctionnalités (Anglais)')
                                    ->simple(true)
                                    ->schema([
                                        Forms\Components\TextInput::make('value')
                                            ->label('Fonctionnalité')
                                            ->required(),
                                    ])
                                    ->columnSpanFull(),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nom')
                    ->label('Nom')
                    ->formatStateUsing(fn ($record) => $record->getTranslatedName())
                    ->searchable(['nom->fr', 'nom->en'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => SouscriptionPlan::TYPES[$state] ?? $state)
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_price')
                    ->label('Prix')
                    ->money('XOF')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_range')
                    ->label('Commission')
                    ->badge(),
                Tables\Columns\IconColumn::make('popular')
                    ->label('Populaire')
                    ->boolean(),
                Tables\Columns\TextColumn::make('periode')
                    ->label('Période')
                    ->formatStateUsing(fn ($state) => SouscriptionPlan::PERIODES[$state] ?? $state)
                    ->badge(),
                Tables\Columns\IconColumn::make('status')
                    ->label('Statut')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Type de plan')
                    ->options(SouscriptionPlan::TYPES),
                Tables\Filters\SelectFilter::make('periode')
                    ->label('Période')
                    ->options(SouscriptionPlan::PERIODES),
                Tables\Filters\TernaryFilter::make('popular')
                    ->label('Plans populaires'),
                Tables\Filters\TernaryFilter::make('status')
                    ->label('Status')
                    ->trueLabel('Actif')
                    ->falseLabel('Inactif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSouscriptionPlans::route('/'),
            'create' => Pages\CreateSouscriptionPlan::route('/create'),
            'edit' => Pages\EditSouscriptionPlan::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
} 