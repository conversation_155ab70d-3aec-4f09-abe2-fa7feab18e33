import React from 'react';
import { PaperClipIcon, EyeIcon } from '@heroicons/react/24/outline';

interface AttachmentDisplayProps {
    attachments: any[];
    messageId: string;
    messageType: 'conversation' | 'dispute';
    className?: string;
    onImageClick?: (url: string, name: string, size?: string) => void;
}

export default function AttachmentDisplay({ 
    attachments, 
    messageId, 
    messageType, 
    className = '',
    onImageClick
}: AttachmentDisplayProps) {

    if (!attachments || attachments.length === 0) {
        return null;
    }

    const getFileUrl = (attachment: any) => {
        let fileName = 'fichier';
        
        if (typeof attachment === 'string') {
            fileName = attachment.split('/').pop() || fileName;
        } else if (attachment && typeof attachment === 'object') {
            fileName = attachment.name || attachment.filename || attachment.original_name || fileName;
        }

        // Pour lorrelei, utiliser l'URL du storage public
        const baseUrl = window.location.origin + '/storage/';
        return baseUrl + attachment;
    };

    const getFileName = (attachment: any) => {
        if (typeof attachment === 'string') {
            return attachment.split('/').pop() || 'Fichier';
        } else if (attachment && typeof attachment === 'object') {
            return attachment.name || attachment.filename || attachment.original_name || 'Fichier';
        }
        return 'Fichier';
    };

    const getFileSize = (attachment: any) => {
        if (attachment && typeof attachment === 'object' && attachment.size) {
            const bytes = parseInt(attachment.size);
            if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(1) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(1) + ' KB';
            } else {
                return bytes + ' B';
            }
        }
        return null;
    };

    const isImage = (fileName: string) => {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        const extension = fileName.split('.').pop()?.toLowerCase();
        return extension && imageExtensions.includes(extension);
    };

    const getFileIcon = (fileName: string) => {
        const extension = fileName.split('.').pop()?.toLowerCase();
        
        switch (extension) {
            case 'pdf':
                return '📄';
            case 'doc':
            case 'docx':
                return '📝';
            case 'xls':
            case 'xlsx':
                return '📊';
            case 'ppt':
            case 'pptx':
                return '📋';
            case 'zip':
            case 'rar':
            case '7z':
                return '🗜️';
            case 'mp4':
            case 'avi':
            case 'mov':
                return '🎥';
            case 'mp3':
            case 'wav':
            case 'ogg':
                return '🎵';
            default:
                return '📎';
        }
    };

    const handleImageClick = (attachment: any) => {
        const fileName = getFileName(attachment);
        const fileUrl = getFileUrl(attachment);
        const fileSize = getFileSize(attachment);
        
        if (onImageClick) {
            onImageClick(fileUrl, fileName, fileSize || undefined);
        }
    };

    const handleFileDownload = (attachment: any) => {
        const fileUrl = getFileUrl(attachment);
        const fileName = getFileName(attachment);
        
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <div className={`mt-2 space-y-2 ${className}`}>
            {attachments.map((attachment: any, index: number) => {
                const fileName = getFileName(attachment);
                const fileUrl = getFileUrl(attachment);
                const fileSize = getFileSize(attachment);
                const isImg = isImage(fileName);

                return (
                    <div key={index} className="flex items-start space-x-2">
                        {isImg ? (
                            // Affichage image avec preview
                            <div className="flex flex-col space-y-1">
                                <div 
                                    className="relative group cursor-pointer"
                                    onClick={() => handleImageClick(attachment)}
                                >
                                    <img
                                        src={fileUrl}
                                        alt={fileName}
                                        className="max-w-xs max-h-32 rounded-lg object-cover border border-gray-200 dark:border-gray-600
                                                 hover:opacity-90 transition-opacity relative z-10"
                                        style={{ 
                                            backgroundColor: 'white',
                                            minHeight: '80px',
                                            minWidth: '80px'
                                        }}
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                            // Afficher un fallback
                                            const fallback = target.nextElementSibling as HTMLElement;
                                            if (fallback) fallback.style.display = 'flex';
                                        }}
                                    />
                                    
                                    {/* Fallback si l'image ne charge pas */}
                                    <div 
                                        className="hidden items-center justify-center w-32 h-20 bg-gray-100 dark:bg-gray-700 
                                                 rounded-lg border border-gray-200 dark:border-gray-600"
                                    >
                                        <span className="text-2xl">🖼️</span>
                                    </div>
                                    
                                    {/* Overlay au hover */}
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 
                                                  rounded-lg transition-all duration-200 flex items-center justify-center z-20">
                                        <EyeIcon className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity drop-shadow-lg" />
                                    </div>
                                </div>
                                
                                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 max-w-xs">
                                    <span className="truncate">{fileName}</span>
                                    {fileSize && <span className="ml-2 flex-shrink-0">{fileSize}</span>}
                                </div>
                            </div>
                        ) : (
                            // Affichage fichier normal
                            <button
                                onClick={() => handleFileDownload(attachment)}
                                className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 
                                         hover:text-blue-800 dark:hover:text-blue-300 hover:underline 
                                         transition-colors cursor-pointer"
                            >
                                <span className="text-lg">{getFileIcon(fileName)}</span>
                                <div className="flex flex-col items-start">
                                    <span className="text-sm font-medium">{fileName}</span>
                                    {fileSize && (
                                        <span className="text-xs text-gray-500 dark:text-gray-400">{fileSize}</span>
                                    )}
                                </div>
                                <PaperClipIcon className="w-4 h-4 flex-shrink-0" />
                            </button>
                        )}
                    </div>
                );
            })}
        </div>
    );
}
