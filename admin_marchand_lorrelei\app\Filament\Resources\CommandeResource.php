<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CommandeResource\Pages;
use App\Models\Commande;
use App\Models\Marchand;
use App\Models\Client;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CommandeResource extends Resource
{
    protected static ?string $model = Commande::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'Commandes';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la Commande')
                            ->schema([
                                Forms\Components\TextInput::make('id')
                                    ->label('ID de la commande')
                                    ->disabled(),

                                Forms\Components\Select::make('client_id')
                                    ->label('Client')
                                    ->options(Client::with('user')->get()->mapWithKeys(function ($client) {
                                        $label = ($client->prenom ?? 'Prénom') . ' ' . ($client->nom ?? 'Nom');
                                        if ($client->user && $client->user->email) {
                                            $label .= ' (' . $client->user->email . ')';
                                        }
                                        return [$client->id => $label];
                                    }))
                                    ->searchable()
                                    ->required(),

                                Forms\Components\Select::make('marchand_id')
                                    ->label('Marchand')
                                    ->options(Marchand::whereNotNull('nomEntreprise')
                                        ->where('nomEntreprise', '!=', '')
                                        ->pluck('nomEntreprise', 'id'))
                                    ->searchable()
                                    ->required(),

                                Forms\Components\Select::make('statut')
                                    ->options([
                                        'EnAttente' => 'En attente',
                                        'EnCoursDeTraitement' => 'En cours de traitement',
                                        'Expédié' => 'Expédié',
                                        'Livré' => 'Livré',
                                        'Annulé' => 'Annulé',
                                        'Remboursé' => 'Remboursé',
                                    ])
                                    ->required(),

                                Forms\Components\TextInput::make('montantTotal')
                                    ->label('Montant total')
                                    ->numeric()
                                    ->prefix('€')
                                    ->required(),

                                Forms\Components\DatePicker::make('dateExpeditionPrevue')
                                    ->label('Date d\'expédition prévue'),

                                Forms\Components\DatePicker::make('dateLivraisonPrevue')
                                    ->label('Date de livraison prévue'),

                                Forms\Components\TextInput::make('codeSuivi')
                                    ->label('Code de suivi')
                                    ->maxLength(100),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Dates')
                            ->schema([
                                Forms\Components\DateTimePicker::make('creeLe')
                                    ->label('Créé le')
                                    ->default(now()),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),

                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(fn (Commande $record): string =>
                        $record->client->prenom . ' ' . $record->client->nom
                    )
                    ->searchable(),

                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Marchand')
                    ->searchable(),

                Tables\Columns\TextColumn::make('montantTotal')
                    ->label('Montant')
                    ->money(fn ($record) => $record->currency ?? 'FCFA')
                    ->sortable(),

                Tables\Columns\SelectColumn::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCoursDeTraitement' => 'En cours de traitement',
                        'Expédié' => 'Expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Remboursé' => 'Remboursé',
                    ])
                    ->sortable(),

                Tables\Columns\TextColumn::make('creeLe')
                    ->label('Date de commande')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('codeSuivi')
                    ->label('Suivi')
                    ->placeholder('Non disponible'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('marchand_id')
                    ->label('Marchand')
                    ->options(Marchand::all()->pluck('nomEntreprise', 'id')),

                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCoursDeTraitement' => 'En cours de traitement',
                        'Expédié' => 'Expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Remboursé' => 'Remboursé',
                    ]),

                Tables\Filters\Filter::make('creeLe')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Depuis le'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Jusqu\'au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('creeLe', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('creeLe', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('update_status')
                        ->label('Mettre à jour le statut')
                        ->form([
                            Forms\Components\Select::make('statut')
                                ->label('Nouveau statut')
                                ->options([
                                    'EnAttente' => 'En attente',
                                    'EnCoursDeTraitement' => 'En cours de traitement',
                                    'Expédié' => 'Expédié',
                                    'Livré' => 'Livré',
                                    'Annulé' => 'Annulé',
                                    'Remboursé' => 'Remboursé',
                                ])
                                ->required(),
                        ])
                        ->action(function (array $data, array $records): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'statut' => $data['statut'],
                                ]);
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommandes::route('/'),
            'create' => Pages\CreateCommande::route('/create'),
            'edit' => Pages\EditCommande::route('/{record}/edit'),
        ];
    }
}
