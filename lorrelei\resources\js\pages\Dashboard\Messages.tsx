import React, { useState, useEffect, useRef } from 'react';
import { Head, usePage } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
    Search,
    Send,
    Paperclip,
    X,
    MessageCircle,
    Clock,
    User,
    Building2,
    ChevronLeft,
    Loader2
} from 'lucide-react';
import axios from 'axios';
import AttachmentDisplay from '@/components/AttachmentDisplay';
import ImageModal, { useImageModal } from '@/components/ImageModal';
import { EnhancedChatSection } from '@/components/Chat/EnhancedChatSection';
import { ConnectionIndicator } from '@/components/WebSocket/ConnectionIndicator';
import { NotificationToast } from '@/components/WebSocket/NotificationToast';
import { useReverbWebSocket } from '@/hooks/useReverbWebSocket';

interface Client {
    id: number;
    nom: string;
    prenom: string;
    user: {
        id: number;
        name: string;
        email: string;
        avatar?: string;
    };
}

interface Marchand {
    id: number;
    nom_entreprise: string;
    user: {
        id: number;
        name: string;
        email: string;
        avatar?: string;
    };
}

interface Conversation {
    id: number;
    sujet: string;
    statut: 'active' | 'fermee' | 'archivee';
    marchand: Marchand;
    date_creation: string;
    date_dernier_message: string;
    messages_non_lus_client: number;
    type_conversation: string;
}

interface Message {
    id: number;
    message: string;
    auteur_type: 'client' | 'marchand';
    auteur_nom: string;
    type_message: string;
    pieces_jointes?: string[];
    lu_par_client: boolean;
    lu_par_marchand: boolean;
    created_at: string;
}

interface ConversationDetail {
    conversation: Conversation;
    messages: Message[];
}

export default function Messages() {
    const [conversations, setConversations] = useState<Conversation[]>([]);
    const [selectedConversation, setSelectedConversation] = useState<ConversationDetail | null>(null);
    const [loading, setLoading] = useState(true);
    const [loadingMessages, setLoadingMessages] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [showMobileConversation, setShowMobileConversation] = useState(false);
    const { isOpen, imageData, openModal, closeModal } = useImageModal();
    const [newMessage, setNewMessage] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [sending, setSending] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // Récupérer l'utilisateur actuel depuis les props Inertia
    const { auth } = usePage().props as any;

    // Hook WebSocket pour les notifications globales
    const { } = useReverbWebSocket({
        userId: auth?.user?.id,
        onMessage: (data) => {
            // Mettre à jour la liste des conversations si un nouveau message arrive
            if (data.conversation) {
                setConversations(prev =>
                    prev.map(conv =>
                        conv.id === data.conversation.id
                            ? { ...conv, ...data.conversation }
                            : conv
                    )
                );
            }
        },
        onNotification: (data) => {
            console.log('Notification globale reçue:', data);
        },
    });

    // Charger les conversations
    const loadConversations = async () => {
        try {
            const response = await axios.get('/api/client-marchand/conversations', {
                params: { search: searchTerm }
            });
            setConversations(response.data.conversations);
        } catch (error) {
            console.error('Erreur lors du chargement des conversations:', error);
        } finally {
            setLoading(false);
        }
    };

    // Charger une conversation spécifique
    const loadConversation = async (id: number) => {
        setLoadingMessages(true);
        try {
            const response = await axios.get(`/api/client-marchand/conversations/${id}`);
            setSelectedConversation(response.data);
            setShowMobileConversation(true);
        } catch (error) {
            console.error('Erreur lors du chargement de la conversation:', error);
        } finally {
            setLoadingMessages(false);
        }
    };

    // Envoyer un message
    const sendMessage = async () => {
        if (!newMessage.trim() && attachments.length === 0) return;
        if (!selectedConversation) return;

        setSending(true);
        try {
            const formData = new FormData();
            formData.append('message', newMessage);
            formData.append('type_message', 'message');

            attachments.forEach((file, index) => {
                formData.append(`pieces_jointes[${index}]`, file);
            });

            const response = await axios.post(
                `/api/client-marchand/conversations/${selectedConversation.conversation.id}/messages`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );

            // Ajouter le nouveau message à la conversation
            setSelectedConversation(prev => prev ? {
                ...prev,
                messages: [...prev.messages, response.data.message]
            } : null);

            // Réinitialiser le formulaire
            setNewMessage('');
            setAttachments([]);

            // Recharger les conversations pour mettre à jour les compteurs
            loadConversations();
        } catch (error) {
            console.error('Erreur lors de l\'envoi du message:', error);
        } finally {
            setSending(false);
        }
    };

    // Envoyer un message via WebSocket (nouvelle fonction)
    const handleRealtimeSendMessage = async (message: string, files?: File[]) => {
        if (!selectedConversation) return;

        const formData = new FormData();
        formData.append('message', message);
        formData.append('type_message', 'message');

        if (files) {
            files.forEach((file, index) => {
                formData.append(`pieces_jointes[${index}]`, file);
            });
        }

        const response = await fetch(
            `/api/client-marchand/conversations/${selectedConversation.conversation.id}/messages`,
            {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: formData,
            }
        );

        if (!response.ok) {
            throw new Error('Erreur lors de l\'envoi du message');
        }

        // Le message sera automatiquement ajouté via WebSocket
        // Recharger les conversations pour mettre à jour les compteurs
        loadConversations();
    };

    // Gérer l'ajout de fichiers
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        setAttachments(prev => [...prev, ...files]);
    };

    // Supprimer un fichier
    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    // Scroll vers le bas des messages
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        loadConversations();
    }, [searchTerm]);

    useEffect(() => {
        scrollToBottom();
    }, [selectedConversation?.messages]);

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 jours
            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: '2-digit' });
        }
    };

    const getStatusBadge = (statut: string) => {
        const config = {
            'active': { label: 'Active', variant: 'default' as const },
            'fermee': { label: 'Fermée', variant: 'secondary' as const },
            'archivee': { label: 'Archivée', variant: 'outline' as const },
        };

        const { label, variant } = config[statut as keyof typeof config] || { label: statut, variant: 'secondary' as const };
        return <Badge variant={variant}>{label}</Badge>;
    };

    return (
        <ClientDashboardLayout
            title="Mes conversations"
            description="Échangez avec les marchands"
        >
            <Head title="Mes conversations" />

            <div className="flex h-[600px] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Liste des conversations */}
                <div className={`w-full md:w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col ${showMobileConversation ? 'hidden md:flex' : 'flex'}`}>
                    {/* Header */}
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Rechercher une conversation..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </div>

                    {/* Liste */}
                    <div className="flex-1 overflow-y-auto">
                        {loading ? (
                            <div className="flex items-center justify-center h-32">
                                <Loader2 className="h-6 w-6 animate-spin" />
                            </div>
                        ) : conversations.length === 0 ? (
                            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                                <MessageCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
                                <p>Aucune conversation trouvée</p>
                            </div>
                        ) : (
                            conversations.map((conversation) => (
                                <div
                                    key={conversation.id}
                                    onClick={() => loadConversation(conversation.id)}
                                    className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                                        selectedConversation?.conversation.id === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                                    }`}
                                >
                                    <div className="flex items-start space-x-3">
                                        {conversation.marchand.user.avatar ? (
                                            <img
                                                src={conversation.marchand.user.avatar}
                                                alt={conversation.marchand.nom_entreprise}
                                                className="w-10 h-10 rounded-full"
                                            />
                                        ) : (
                                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                <Building2 className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                                            </div>
                                        )}

                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                    {conversation.marchand.nom_entreprise}
                                                </h3>
                                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                                    {formatDate(conversation.date_dernier_message)}
                                                </span>
                                            </div>

                                            <p className="text-sm text-gray-600 dark:text-gray-300 truncate mt-1">
                                                {conversation.sujet}
                                            </p>

                                            <div className="flex items-center justify-between mt-2">
                                                {getStatusBadge(conversation.statut)}
                                                {conversation.messages_non_lus_client > 0 && (
                                                    <Badge variant="destructive" className="text-xs">
                                                        {conversation.messages_non_lus_client} nouveau{conversation.messages_non_lus_client > 1 ? 'x' : ''}
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                {/* Zone de conversation */}
                <div className={`flex-1 flex flex-col ${showMobileConversation ? 'flex' : 'hidden md:flex'}`}>
                    {selectedConversation ? (
                        <>
                            {/* Header de la conversation */}
                            <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="md:hidden"
                                            onClick={() => setShowMobileConversation(false)}
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                        </Button>

                                        {selectedConversation.conversation.marchand.user.avatar ? (
                                            <img
                                                src={selectedConversation.conversation.marchand.user.avatar}
                                                alt={selectedConversation.conversation.marchand.nom_entreprise}
                                                className="w-8 h-8 rounded-full"
                                            />
                                        ) : (
                                            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                <Building2 className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                                            </div>
                                        )}

                                        <div>
                                            <h2 className="text-sm font-semibold text-gray-900 dark:text-white">
                                                {selectedConversation.conversation.marchand.nom_entreprise}
                                            </h2>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                                {selectedConversation.conversation.sujet}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <ConnectionIndicator size="sm" />
                                        {getStatusBadge(selectedConversation.conversation.statut)}
                                    </div>
                                </div>
                            </div>

                            {/* Chat WebSocket amélioré */}
                            <div className="flex-1 flex flex-col">
                                {loadingMessages ? (
                                    <div className="flex items-center justify-center h-32">
                                        <Loader2 className="h-6 w-6 animate-spin" />
                                    </div>
                                ) : (
                                    <EnhancedChatSection
                                        conversationId={selectedConversation.conversation.id.toString()}
                                        messages={selectedConversation.messages}
                                        currentUserId={auth?.user?.id?.toString() || ''}
                                        onSendMessage={handleRealtimeSendMessage}
                                        onImageClick={openModal}
                                        isActive={selectedConversation.conversation.statut === 'active'}
                                        className="h-full"
                                    />
                                )}
                            </div>
                        </>
                    ) : (
                        <div className="flex-1 flex items-center justify-center text-gray-500 dark:text-gray-400">
                            <div className="text-center">
                                <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>Sélectionnez une conversation pour commencer</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Modal d'image */}
            {imageData && (
                <ImageModal
                    isOpen={isOpen}
                    onClose={closeModal}
                    imageUrl={imageData.url}
                    imageName={imageData.name}
                    imageSize={imageData.size}
                />
            )}

            {/* Notifications WebSocket */}
            {auth?.user && (
                <NotificationToast
                    userId={auth.user.id.toString()}
                    position="top-right"
                    maxNotifications={3}
                    autoHideDelay={5000}
                />
            )}
        </ClientDashboardLayout>
    );
}
