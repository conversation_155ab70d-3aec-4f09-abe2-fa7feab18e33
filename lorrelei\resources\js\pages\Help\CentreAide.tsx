import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    HelpCircle,
    Search,
    BookOpen,
    MessageSquare,
    ShoppingCart,
    CreditCard,
    Truck,
    Shield,
    Users,
    Settings,
    ChevronRight,
    Star,
    Clock,
    CheckCircle,
    AlertCircle,
    Phone,
    Mail,
    MessageCircle
} from 'lucide-react';

interface FAQItem {
    question: string;
    answer: string;
    category: string;
    popular?: boolean;
}

interface HelpCategory {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
    color: string;
    articles: number;
}

const helpCategories: HelpCategory[] = [
    {
        id: 'commandes',
        title: 'Commandes & Livraisons',
        description: 'Tout sur le processus de commande et de livraison',
        icon: <ShoppingCart className="h-6 w-6" />,
        color: 'bg-blue-500',
        articles: 12
    },
    {
        id: 'paiements',
        title: 'Paiements & Facturation',
        description: 'Méthodes de paiement, factures et remboursements',
        icon: <CreditCard className="h-6 w-6" />,
        color: 'bg-green-500',
        articles: 8
    },
    {
        id: 'compte',
        title: 'Mon Compte',
        description: 'Gestion de votre profil et paramètres',
        icon: <Users className="h-6 w-6" />,
        color: 'bg-purple-500',
        articles: 6
    },
    {
        id: 'marchand',
        title: 'Espace Marchand',
        description: 'Guide pour les vendeurs sur la plateforme',
        icon: <BookOpen className="h-6 w-6" />,
        color: 'bg-orange-500',
        articles: 15
    },
    {
        id: 'securite',
        title: 'Sécurité & Confidentialité',
        description: 'Protection de vos données et transactions',
        icon: <Shield className="h-6 w-6" />,
        color: 'bg-red-500',
        articles: 5
    },
    {
        id: 'technique',
        title: 'Support Technique',
        description: 'Problèmes techniques et dépannage',
        icon: <Settings className="h-6 w-6" />,
        color: 'bg-gray-500',
        articles: 10
    }
];

const popularFAQs: FAQItem[] = [
    {
        question: "Comment passer une commande sur Lorrelei ?",
        answer: "Pour passer une commande, ajoutez les produits souhaités à votre panier, puis cliquez sur 'Commander'. Suivez les étapes de checkout en renseignant vos informations de livraison et de paiement.",
        category: "commandes",
        popular: true
    },
    {
        question: "Quels sont les délais de livraison ?",
        answer: "Les délais de livraison varient selon le marchand et votre localisation. En général, comptez 2-5 jours ouvrables pour les livraisons standard et 24-48h pour les livraisons express.",
        category: "commandes",
        popular: true
    },
    {
        question: "Comment devenir marchand sur Lorrelei ?",
        answer: "Pour devenir marchand, créez un compte sur notre plateforme marchand, remplissez le formulaire d'inscription en 4 étapes, soumettez vos documents et attendez la validation de notre équipe (24-48h).",
        category: "marchand",
        popular: true
    },
    {
        question: "Quelles sont les méthodes de paiement acceptées ?",
        answer: "Nous acceptons les cartes bancaires (Visa, Mastercard), les virements bancaires, Orange Money, MTN Money et les paiements en espèces à la livraison selon les marchands.",
        category: "paiements",
        popular: true
    },
    {
        question: "Comment suivre ma commande ?",
        answer: "Connectez-vous à votre compte et accédez à la section 'Mes commandes'. Vous y trouverez le statut en temps réel de toutes vos commandes avec les informations de suivi.",
        category: "commandes",
        popular: true
    },
    {
        question: "Que faire si je ne reçois pas ma commande ?",
        answer: "Si votre commande n'arrive pas dans les délais prévus, contactez d'abord le marchand via la messagerie. Si le problème persiste, ouvrez un litige depuis votre espace client.",
        category: "commandes",
        popular: true
    }
];

const contactOptions = [
    {
        title: "Chat en Direct",
        description: "Discutez avec notre équipe support",
        icon: <MessageCircle className="h-6 w-6" />,
        action: "Démarrer le chat",
        available: "Lun-Ven 8h-18h",
        color: "bg-green-500"
    },
    {
        title: "Email Support",
        description: "Envoyez-nous un email détaillé",
        icon: <Mail className="h-6 w-6" />,
        action: "<EMAIL>",
        available: "Réponse sous 24h",
        color: "bg-blue-500"
    },
    {
        title: "Téléphone",
        description: "Appelez notre service client",
        icon: <Phone className="h-6 w-6" />,
        action: "+237 6XX XXX XXX",
        available: "Lun-Ven 8h-17h",
        color: "bg-purple-500"
    }
];

export default function CentreAide() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

    const filteredFAQs = popularFAQs.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
        <AppLayout>
            <Head title="Centre d'Aide - Lorrelei" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <div className="flex justify-center mb-6">
                            <div className="bg-blue-100 p-4 rounded-full">
                                <HelpCircle className="h-12 w-12 text-blue-600" />
                            </div>
                        </div>
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Centre d'Aide Lorrelei
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                            Trouvez rapidement des réponses à toutes vos questions. 
                            Notre équipe support est là pour vous accompagner.
                        </p>
                        
                        {/* Search Bar */}
                        <div className="max-w-2xl mx-auto relative">
                            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                            <Input
                                type="text"
                                placeholder="Rechercher dans l'aide..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-12 pr-4 py-3 text-lg"
                            />
                        </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">98%</div>
                                <div className="text-sm text-gray-600">Problèmes Résolus</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">< 2h</div>
                                <div className="text-sm text-gray-600">Temps de Réponse</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Star className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                                <div className="text-2xl font-bold text-gray-900">4.9/5</div>
                                <div className="text-sm text-gray-600">Satisfaction Client</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Help Categories */}
                    <div className="mb-12">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                            Parcourir par Catégorie
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {helpCategories.map((category) => (
                                <Card key={category.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                                    <CardContent className="p-6">
                                        <div className="flex items-start space-x-4">
                                            <div className={`${category.color} p-3 rounded-lg text-white`}>
                                                {category.icon}
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-gray-900 mb-2">
                                                    {category.title}
                                                </h3>
                                                <p className="text-sm text-gray-600 mb-3">
                                                    {category.description}
                                                </p>
                                                <div className="flex items-center justify-between">
                                                    <Badge variant="secondary">
                                                        {category.articles} articles
                                                    </Badge>
                                                    <ChevronRight className="h-4 w-4 text-gray-400" />
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>

                    {/* Popular FAQs */}
                    <div className="mb-12">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                            Questions Fréquentes
                        </h2>
                        <div className="space-y-4">
                            {filteredFAQs.map((faq, index) => (
                                <Card key={index} className="overflow-hidden">
                                    <CardHeader className="cursor-pointer hover:bg-gray-50">
                                        <div className="flex items-center justify-between">
                                            <CardTitle className="text-lg">{faq.question}</CardTitle>
                                            {faq.popular && (
                                                <Badge variant="secondary" className="ml-2">
                                                    <Star className="h-3 w-3 mr-1" />
                                                    Populaire
                                                </Badge>
                                            )}
                                        </div>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-700">{faq.answer}</p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>

                    {/* Contact Support */}
                    <Card className="mb-12">
                        <CardHeader className="text-center">
                            <CardTitle className="text-2xl">Besoin d'Aide Supplémentaire ?</CardTitle>
                            <CardDescription>
                                Notre équipe support est disponible pour vous aider
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-3 gap-6">
                                {contactOptions.map((option, index) => (
                                    <div key={index} className="text-center">
                                        <div className={`${option.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white`}>
                                            {option.icon}
                                        </div>
                                        <h3 className="font-semibold text-gray-900 mb-2">{option.title}</h3>
                                        <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                                        <p className="text-sm font-medium text-gray-900 mb-2">{option.action}</p>
                                        <p className="text-xs text-gray-500">{option.available}</p>
                                        <Button variant="outline" size="sm" className="mt-3">
                                            Contacter
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Emergency Alert */}
                    <Card className="border-orange-200 bg-orange-50">
                        <CardContent className="p-6">
                            <div className="flex items-start space-x-4">
                                <AlertCircle className="h-6 w-6 text-orange-600 mt-1" />
                                <div>
                                    <h3 className="font-semibold text-orange-900 mb-2">
                                        Problème Urgent ?
                                    </h3>
                                    <p className="text-orange-800 mb-4">
                                        Pour les problèmes urgents (paiements bloqués, commandes perdues, sécurité),
                                        contactez immédiatement notre support prioritaire.
                                    </p>
                                    <Button variant="outline" className="border-orange-300 text-orange-700 hover:bg-orange-100">
                                        Support Prioritaire
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
