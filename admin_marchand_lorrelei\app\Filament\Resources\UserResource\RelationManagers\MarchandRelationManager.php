<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class MarchandRelationManager extends RelationManager
{
    protected static string $relationship = 'marchand';

    protected static ?string $recordTitleAttribute = 'nomEntreprise';

    protected static ?string $title = 'Informations marchand';

    public function isVisible(): bool
    {
        return $this->ownerRecord->role === 'Marchand';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nomEntreprise')
                    ->label('Nom de l\'entreprise')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('siret')
                    ->label('SIRET')
                    ->maxLength(20),
                Forms\Components\TextInput::make('telephone')
                    ->label('Téléphone')
                    ->tel()
                    ->maxLength(20),
                Forms\Components\TextInput::make('siteWeb')
                    ->label('Site web')
                    ->url()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Nom de l\'entreprise')
                    ->searchable(),
                Tables\Columns\TextColumn::make('siret')
                    ->label('SIRET'),
                Tables\Columns\TextColumn::make('telephone')
                    ->label('Téléphone'),
                Tables\Columns\TextColumn::make('siteWeb')
                    ->label('Site web')
                    ->url(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
