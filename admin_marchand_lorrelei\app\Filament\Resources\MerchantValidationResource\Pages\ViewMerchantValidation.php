<?php

namespace App\Filament\Resources\MerchantValidationResource\Pages;

use App\Filament\Resources\MerchantValidationResource;
use App\Services\MerchantValidationService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class ViewMerchantValidation extends ViewRecord
{
    protected static string $resource = MerchantValidationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),

            Actions\Action::make('download_all_documents')
                ->label('Télécharger tous les documents')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->url(fn () => route('admin.merchant-validation.download-all-documents', $this->record))
                ->openUrlInNewTab()
                ->visible(fn () => $this->record->documents()->exists()),
            
            Actions\Action::make('approve')
                ->label('Approuver le marchand')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Approuver le marchand')
                ->modalDescription('Êtes-vous sûr de vouloir approuver ce marchand ? Il aura accès à son dashboard.')
                ->modalSubmitActionLabel('Approuver')
                ->visible(fn () => $this->record->status === 'EN_ATTENTE_VALIDATION')
                ->action(function () {
                    $service = new MerchantValidationService();
                    $result = $service->approuverMarchand($this->record->id, Auth::id());
                    
                    if ($result['success']) {
                        Notification::make()
                            ->title('Marchand approuvé avec succès')
                            ->body('Le marchand a été notifié par email et peut maintenant accéder à son dashboard.')
                            ->success()
                            ->send();
                        
                        $this->redirect($this->getResource()::getUrl('index'));
                    } else {
                        Notification::make()
                            ->title('Erreur lors de l\'approbation')
                            ->body($result['message'] ?? 'Une erreur est survenue')
                            ->danger()
                            ->send();
                    }
                }),
            
            Actions\Action::make('reject')
                ->label('Rejeter le marchand')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->form([
                    \Filament\Forms\Components\Textarea::make('rejection_reason')
                        ->label('Raison du rejet')
                        ->required()
                        ->maxLength(1000)
                        ->placeholder('Expliquez pourquoi ce marchand est rejeté...'),
                ])
                ->modalHeading('Rejeter le marchand')
                ->modalDescription('Veuillez expliquer la raison du rejet. Le marchand sera notifié par email.')
                ->modalSubmitActionLabel('Rejeter')
                ->visible(fn () => $this->record->status === 'EN_ATTENTE_VALIDATION')
                ->action(function (array $data) {
                    $service = new MerchantValidationService();
                    $result = $service->rejeterMarchand($this->record->id, Auth::id(), $data['rejection_reason']);
                    
                    if ($result['success']) {
                        Notification::make()
                            ->title('Marchand rejeté')
                            ->body('Le marchand a été notifié par email de la raison du rejet.')
                            ->success()
                            ->send();
                        
                        $this->redirect($this->getResource()::getUrl('index'));
                    } else {
                        Notification::make()
                            ->title('Erreur lors du rejet')
                            ->body($result['message'] ?? 'Une erreur est survenue')
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
}
