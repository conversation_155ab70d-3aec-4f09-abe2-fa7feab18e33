<?php

use App\Http\Controllers\SellerRegistrationController;
use App\Http\Controllers\SellerDashboardController;
use App\Http\Controllers\WelcomeController;
use App\Http\Controllers\MerchantValidationController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes - Plateforme Marchand (seller.lorelei.com)
|--------------------------------------------------------------------------
|
| Ce fichier contient les routes pour la plateforme marchand séparée.
|
| Structure des URLs :
| - / : Page d'accueil
| - /seller/* : Processus d'inscription marchand
| - /dashboard/* : Dashboard Inertia pour les marchands
| - /marchand/* : Panel Filament pour les marchands (géré par Filament)
| - /admin/* : Panel Filament pour les administrateurs (géré par Filament)
|
*/

/*
|--------------------------------------------------------------------------
| Page d'accueil
|--------------------------------------------------------------------------
*/
Route::get('/', [WelcomeController::class, 'index'])
    ->name('welcome');

/*
|--------------------------------------------------------------------------
| Routes d'inscription et validation marchand (Inertia)
|--------------------------------------------------------------------------
*/
Route::middleware(['auth', 'merchant.validation'])->prefix('seller')->name('seller.')->group(function () {
    // Processus de validation en 4 étapes
    Route::get('/welcome', [MerchantValidationController::class, 'showWelcome'])->name('welcome');

    // Étape 1 : Informations personnelles
    Route::get('/personal-info', [MerchantValidationController::class, 'showPersonalInfo'])->name('personal-info');
    Route::post('/personal-info', [MerchantValidationController::class, 'submitPersonalInfo'])->name('personal-info.store');

    // Étape 2 : Facturation et business
    Route::get('/billing', [MerchantValidationController::class, 'showBilling'])->name('billing');
    Route::post('/billing', [MerchantValidationController::class, 'submitBilling'])->name('billing.store');

    // Étape 3 : Configuration boutique
    Route::get('/store-setup', [MerchantValidationController::class, 'showStoreSetup'])->name('store-setup');
    Route::post('/store-setup', [MerchantValidationController::class, 'submitStoreSetup'])->name('store-setup.store');

    // Étape 4 : Documents et finalisation
    Route::get('/documents', [MerchantValidationController::class, 'showDocuments'])->name('documents');
    Route::post('/documents', [MerchantValidationController::class, 'uploadDocument'])->name('upload-document');
    Route::post('/submit-for-review', [MerchantValidationController::class, 'submitForReview'])->name('submit-for-review');
    Route::get('/submission-complete', [MerchantValidationController::class, 'showSubmissionComplete'])->name('submission-complete');

    // Routes de compatibilité (à supprimer plus tard)
    Route::get('/business-info', [MerchantValidationController::class, 'showBusinessInfo'])->name('business-info');
    Route::post('/business-info/store', [MerchantValidationController::class, 'submitBusinessInfo'])->name('business-info.store');

    // Pages d'état
    Route::get('/rejected', function () {
        return Inertia::render('SellerRegistration/Rejected');
    })->name('rejected');

    Route::get('/suspended', function () {
        return Inertia::render('SellerRegistration/Suspended');
    })->name('suspended');
});

/*
|--------------------------------------------------------------------------
| Routes Admin spécifiques
|--------------------------------------------------------------------------
*/

// Routes de téléchargement des documents de validation marchand
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/merchant-validation/{validation}/document/{document}/download',
        [\App\Http\Controllers\Admin\MerchantValidationController::class, 'downloadDocument'])
        ->name('merchant-validation.download-document');

    Route::get('/merchant-validation/{validation}/document/{document}/preview',
        [\App\Http\Controllers\Admin\MerchantValidationController::class, 'previewDocument'])
        ->name('merchant-validation.preview-document');

    Route::get('/merchant-validation/{validation}/documents/download-all',
        [\App\Http\Controllers\Admin\MerchantValidationController::class, 'downloadAllDocuments'])
        ->name('merchant-validation.download-all-documents');
});

/*
|--------------------------------------------------------------------------
| Redirections et routes de compatibilité
|--------------------------------------------------------------------------
*/

// Redirection du dashboard par défaut vers le dashboard marchand
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return redirect()->route('welcome');
    })->name('dashboard');
});

// Redirection pour les marchands qui tentent d'accéder à l'ancien dashboard
Route::middleware(['auth'])->get('/old-dashboard', function () {
    return redirect()->route('welcome');
});

/*
|--------------------------------------------------------------------------
| Inclusion des autres fichiers de routes
|--------------------------------------------------------------------------
*/
require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/admin.php';

// Routes API pour les dashboards React
Route::middleware(['auth:web'])->prefix('api')->group(function () {

    // Routes pour les messages (Marchand)
    Route::prefix('messages')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\MessageController::class, 'index']);
        Route::get('/stats', [App\Http\Controllers\Api\MessageController::class, 'stats']);
        Route::get('/{id}', [App\Http\Controllers\Api\MessageController::class, 'show']);
        Route::post('/{id}/messages', [App\Http\Controllers\Api\MessageController::class, 'store']);
        Route::patch('/{id}/close', [App\Http\Controllers\Api\MessageController::class, 'close']);
        Route::post('/typing', [App\Http\Controllers\Api\MessageController::class, 'typing']);
        Route::get('/{messageId}/attachments/{filename}', [App\Http\Controllers\Api\MessageController::class, 'downloadAttachment']);
    });

    // Routes pour les litiges (Admin)
    Route::middleware(['admin'])->prefix('disputes')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\DisputeController::class, 'index']);
        Route::get('/stats', [App\Http\Controllers\Api\DisputeController::class, 'stats']);
        Route::get('/admins', [App\Http\Controllers\Api\DisputeController::class, 'admins']);
        Route::get('/{id}', [App\Http\Controllers\Api\DisputeController::class, 'show']);
        Route::post('/{id}/messages', [App\Http\Controllers\Api\DisputeController::class, 'store']);
        Route::patch('/{id}/assign', [App\Http\Controllers\Api\DisputeController::class, 'assign']);
        Route::patch('/{id}/status', [App\Http\Controllers\Api\DisputeController::class, 'updateStatus']);
        Route::get('/{messageId}/attachments/{filename}', [App\Http\Controllers\Api\DisputeController::class, 'downloadAttachment']);
    });

    // Webhook pour les notifications temps réel
    Route::post('/webhook/realtime-notification', [App\Http\Controllers\Api\WebhookController::class, 'realtimeNotification']);
});

// Routes pour les dashboards React
Route::middleware(['auth:web'])->group(function () {

    // Dashboard Messages Marchand
    Route::get('/dashboard/messages', function () {
        return Inertia::render('Dashboard/Messages');
    })->name('dashboard.messages');

    // Dashboard Litiges Admin
    Route::middleware(['admin'])->get('/dashboard/disputes', function () {
        return Inertia::render('Dashboard/Disputes');
    })->name('dashboard.disputes');
});

// Routes de test pour le développement (à supprimer en production)
if (config('app.debug')) {
    require __DIR__.'/test-dashboard.php';
}
