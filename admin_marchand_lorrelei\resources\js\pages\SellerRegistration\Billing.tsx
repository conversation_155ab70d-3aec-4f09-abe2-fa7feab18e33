import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, Info, MapPin } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import SellerHeader from '@/components/SellerHeader';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    validation?: any;
    billingInfo: {
        type_business?: string;
        nomEntreprise?: string;
        pays_business?: string;
        ville_business?: string;
        telephone_principal?: string;
        email_business?: string;
        chiffre_affaires_estime?: number;
        nombre_employes?: number;
        methode_paiement_preferee?: string;
        iban?: string;
        nom_titulaire_compte?: string;
        numero_orange_money?: string;
        numero_mtn_money?: string;
    };
    countries: Record<string, string>;
}

export default function Billing({ user, validation, billingInfo, countries }: Props) {
    const { translate } = useTranslation();

    const { data, setData, post, processing, errors } = useForm({
        type_business: billingInfo?.type_business || '',
        nomEntreprise: billingInfo?.nomEntreprise || '',
        pays_business: billingInfo?.pays_business || '',
        ville_business: billingInfo?.ville_business || '',
        telephone_principal: billingInfo?.telephone_principal || '',
        email_business: billingInfo?.email_business || '',
        chiffre_affaires_estime: billingInfo?.chiffre_affaires_estime || '',
        nombre_employes: billingInfo?.nombre_employes || '',
        methode_paiement_preferee: billingInfo?.methode_paiement_preferee || '',
        iban: billingInfo?.iban || '',
        nom_titulaire_compte: billingInfo?.nom_titulaire_compte || '',
        numero_orange_money: billingInfo?.numero_orange_money || '',
        numero_mtn_money: billingInfo?.numero_mtn_money || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.billing.store'), {
            onError: (errors) => {
                // Scroll vers le premier champ avec erreur
                setTimeout(() => {
                    const firstErrorField = Object.keys(errors)[0];
                    if (firstErrorField) {
                        const element = document.getElementById(firstErrorField);
                        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        element?.focus();
                    }
                }, 100);
            },
        });
    };



    return (
        <>
            <Head title={translate('seller.billing.title')} />

            <div className="min-h-screen bg-background text-foreground">
                <SellerHeader />

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Informations personnelles
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 2 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        2
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Facturation
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 3 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Boutique
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Vérification
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={50} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <CreditCard className="w-5 h-5 text-primary" />
                                <span>Informations business et facturation</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Renseignez les informations de votre entreprise et vos préférences de paiement.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Afficher les erreurs globales */}
                                {(errors as any).error && (
                                    <Alert variant="destructive" className="mb-6">
                                        <AlertDescription>{(errors as any).error}</AlertDescription>
                                    </Alert>
                                )}

                                {/* Type de business */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        Type d'entreprise
                                    </h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="type_business">Type de business *</Label>
                                        <div className={errors?.type_business ? 'select-error' : ''}>
                                            <Select value={data.type_business} onValueChange={(value) => setData('type_business', value)}>
                                                <SelectTrigger className={errors?.type_business ? 'field-error' : ''}>
                                                    <SelectValue placeholder="Sélectionnez le type de votre business" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="individuel">Entrepreneur individuel</SelectItem>
                                                    <SelectItem value="entreprise">Entreprise</SelectItem>
                                                    <SelectItem value="cooperative">Coopérative</SelectItem>
                                                    <SelectItem value="grande_entreprise">Grande entreprise</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        {errors?.type_business && (
                                            <p className="error-message">{errors.type_business}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Informations entreprise */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        Informations de l'entreprise
                                    </h3>

                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="nomEntreprise">Nom de l'entreprise *</Label>
                                            <Input
                                                id="nomEntreprise"
                                                value={data.nomEntreprise}
                                                onChange={(e) => setData('nomEntreprise', e.target.value)}
                                                placeholder="Nom de votre entreprise"
                                                className={errors?.nomEntreprise ? 'field-error' : ''}
                                            />
                                            {errors?.nomEntreprise && (
                                                <p className="error-message">{errors.nomEntreprise}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pays_business">Pays d'activité *</Label>
                                            <div className={errors?.pays_business ? 'select-error' : ''}>
                                                <Select value={data.pays_business} onValueChange={(value) => setData('pays_business', value)}>
                                                    <SelectTrigger className={errors?.pays_business ? 'field-error' : ''}>
                                                        <SelectValue placeholder="Sélectionnez le pays d'activité" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(countries).map(([code, name]) => (
                                                            <SelectItem key={code} value={code}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            {errors?.pays_business && (
                                                <p className="error-message">{errors.pays_business}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="ville_business">Ville d'activité *</Label>
                                            <Input
                                                id="ville_business"
                                                value={data.ville_business}
                                                onChange={(e) => setData('ville_business', e.target.value)}
                                                placeholder="Ville où vous exercez"
                                                className={errors?.ville_business ? 'field-error' : ''}
                                            />
                                            {errors?.ville_business && (
                                                <p className="error-message">{errors.ville_business}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="telephone_principal">Téléphone principal *</Label>
                                            <Input
                                                id="telephone_principal"
                                                value={data.telephone_principal}
                                                onChange={(e) => setData('telephone_principal', e.target.value)}
                                                placeholder="Ex: +237 6XX XXX XXX"
                                                className={errors?.telephone_principal ? 'field-error' : ''}
                                            />
                                            {errors?.telephone_principal && (
                                                <p className="error-message">{errors.telephone_principal}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email_business">Email professionnel</Label>
                                            <Input
                                                id="email_business"
                                                type="email"
                                                value={data.email_business}
                                                onChange={(e) => setData('email_business', e.target.value)}
                                                placeholder="<EMAIL>"
                                                className={errors?.email_business ? 'field-error' : ''}
                                            />
                                            {errors?.email_business && (
                                                <p className="error-message">{errors.email_business}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="chiffre_affaires_estime">Chiffre d'affaires estimé (FCFA)</Label>
                                            <Input
                                                id="chiffre_affaires_estime"
                                                type="number"
                                                value={data.chiffre_affaires_estime}
                                                onChange={(e) => setData('chiffre_affaires_estime', e.target.value)}
                                                placeholder="Ex: 5000000"
                                                className={errors?.chiffre_affaires_estime ? 'field-error' : ''}
                                            />
                                            {errors?.chiffre_affaires_estime && (
                                                <p className="error-message">{errors.chiffre_affaires_estime}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="nombre_employes">Nombre d'employés</Label>
                                            <Input
                                                id="nombre_employes"
                                                type="number"
                                                value={data.nombre_employes}
                                                onChange={(e) => setData('nombre_employes', e.target.value)}
                                                placeholder="Ex: 5"
                                                className={errors?.nombre_employes ? 'field-error' : ''}
                                            />
                                            {errors?.nombre_employes && (
                                                <p className="error-message">{errors.nombre_employes}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Informations de paiement */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        Méthode de paiement préférée
                                    </h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="methode_paiement_preferee">Méthode de paiement *</Label>
                                        <div className={errors?.methode_paiement_preferee ? 'select-error' : ''}>
                                            <Select value={data.methode_paiement_preferee} onValueChange={(value) => setData('methode_paiement_preferee', value)}>
                                                <SelectTrigger className={errors?.methode_paiement_preferee ? 'field-error' : ''}>
                                                    <SelectValue placeholder="Sélectionnez votre méthode de paiement" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="rib">Virement bancaire (RIB)</SelectItem>
                                                    <SelectItem value="orange_money">Orange Money</SelectItem>
                                                    <SelectItem value="mtn_money">MTN Mobile Money</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        {errors?.methode_paiement_preferee && (
                                            <p className="error-message">{errors.methode_paiement_preferee}</p>
                                        )}
                                    </div>

                                    {/* Informations bancaires - RIB */}
                                    {data.methode_paiement_preferee === 'rib' && (
                                        <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                                            <h4 className="font-medium">Informations bancaires</h4>

                                            <div className="grid md:grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="iban">IBAN *</Label>
                                                    <Input
                                                        id="iban"
                                                        value={data.iban}
                                                        onChange={(e) => setData('iban', e.target.value)}
                                                        placeholder="Ex: FR14 2004 1010 0505 0001 3M02 606"
                                                        className={errors?.iban ? 'field-error' : ''}
                                                    />
                                                    {errors?.iban && (
                                                        <p className="error-message">{errors.iban}</p>
                                                    )}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="nom_titulaire_compte">Nom du titulaire *</Label>
                                                    <Input
                                                        id="nom_titulaire_compte"
                                                        value={data.nom_titulaire_compte}
                                                        onChange={(e) => setData('nom_titulaire_compte', e.target.value)}
                                                        placeholder="Nom complet du titulaire du compte"
                                                        className={errors?.nom_titulaire_compte ? 'field-error' : ''}
                                                    />
                                                    {errors?.nom_titulaire_compte && (
                                                        <p className="error-message">{errors.nom_titulaire_compte}</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Orange Money */}
                                    {data.methode_paiement_preferee === 'orange_money' && (
                                        <div className="space-y-4 p-4 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                                            <h4 className="font-medium text-orange-800 dark:text-orange-200">Orange Money</h4>

                                            <div className="space-y-2">
                                                <Label htmlFor="numero_orange_money">Numéro Orange Money *</Label>
                                                <Input
                                                    id="numero_orange_money"
                                                    value={data.numero_orange_money}
                                                    onChange={(e) => setData('numero_orange_money', e.target.value)}
                                                    placeholder="Ex: +237 6XX XXX XXX"
                                                    className={errors?.numero_orange_money ? 'field-error' : ''}
                                                />
                                                {errors?.numero_orange_money && (
                                                    <p className="error-message">{errors.numero_orange_money}</p>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* MTN Mobile Money */}
                                    {data.methode_paiement_preferee === 'mtn_money' && (
                                        <div className="space-y-4 p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                                            <h4 className="font-medium text-yellow-800 dark:text-yellow-200">MTN Mobile Money</h4>

                                            <div className="space-y-2">
                                                <Label htmlFor="numero_mtn_money">Numéro MTN Mobile Money *</Label>
                                                <Input
                                                    id="numero_mtn_money"
                                                    value={data.numero_mtn_money}
                                                    onChange={(e) => setData('numero_mtn_money', e.target.value)}
                                                    placeholder="Ex: +237 6XX XXX XXX"
                                                    className={errors?.numero_mtn_money ? 'field-error' : ''}
                                                />
                                                {errors?.numero_mtn_money && (
                                                    <p className="error-message">{errors.numero_mtn_money}</p>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.personal-info')}>
                                            Retour
                                        </a>
                                    </Button>

                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing ? 'Enregistrement...' : 'Continuer'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
