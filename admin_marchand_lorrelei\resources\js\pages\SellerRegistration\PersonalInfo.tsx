import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { User, MapPin, Phone, Calendar } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import SellerHeader from '@/components/SellerHeader';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    validation?: any;
    personalInfo: {
        prenom?: string;
        nom?: string;
        date_naissance?: string;
        pays_citoyennete?: string;
        pays_naissance?: string;
        adresse_ligne1?: string;
        adresse_ligne2?: string;
        ville?: string;
        region?: string;
        code_postal?: string;
        pays_adresse?: string;
        telephone_verification?: string;
    };
    countries: Record<string, string>;
}

export default function PersonalInfo({ user, validation, personalInfo, countries }: Props) {
    const { translate } = useTranslation();

    const { data, setData, post, processing, errors } = useForm({
        prenom: personalInfo?.prenom || '',
        nom: personalInfo?.nom || '',
        date_naissance: personalInfo?.date_naissance || '',
        pays_citoyennete: personalInfo?.pays_citoyennete || '',
        pays_naissance: personalInfo?.pays_naissance || '',
        adresse_ligne1: personalInfo?.adresse_ligne1 || '',
        adresse_ligne2: personalInfo?.adresse_ligne2 || '',
        ville: personalInfo?.ville || '',
        region: personalInfo?.region || '',
        code_postal: personalInfo?.code_postal || '',
        pays_adresse: personalInfo?.pays_adresse || '',
        telephone_verification: personalInfo?.telephone_verification || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.personal-info.store'), {
            onError: (errors) => {
                // Scroll vers le premier champ avec erreur
                setTimeout(() => {
                    const firstErrorField = Object.keys(errors)[0];
                    if (firstErrorField) {
                        const element = document.getElementById(firstErrorField);
                        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        element?.focus();
                    }
                }, 100);
            },
        });
    };

    return (
        <>
            <Head title="Informations personnelles - Inscription marchand" />

            <div className="min-h-screen bg-background text-foreground">
                <SellerHeader />

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        1
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Informations personnelles
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 2 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        2
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Facturation
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 3 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Boutique
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Vérification
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={25} className="h-2" />
                    </div>

                    {/* Afficher les erreurs globales */}
                    {(errors as any).error && (
                        <Alert variant="destructive" className="mb-6">
                            <AlertDescription>{(errors as any).error}</AlertDescription>
                        </Alert>
                    )}

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <User className="w-5 h-5 text-primary" />
                                <span>Informations personnelles</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Renseignez vos informations personnelles pour commencer votre inscription en tant que marchand.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Informations de base */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                                        Identité
                                    </h3>

                                    <div className="grid md:grid-cols-3 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="prenom">Prénom *</Label>
                                            <Input
                                                id="prenom"
                                                value={data.prenom}
                                                onChange={(e) => setData('prenom', e.target.value)}
                                                placeholder="Votre prénom"
                                                className={errors?.prenom ? 'field-error' : ''}
                                            />
                                            {errors?.prenom && (
                                                <p className="error-message">{errors.prenom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="nom">Nom *</Label>
                                            <Input
                                                id="nom"
                                                value={data.nom}
                                                onChange={(e) => setData('nom', e.target.value)}
                                                placeholder="Votre nom de famille"
                                                className={errors?.nom ? 'field-error' : ''}
                                            />
                                            {errors?.nom && (
                                                <p className="error-message">{errors.nom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="date_naissance">Date de naissance *</Label>
                                            <Input
                                                id="date_naissance"
                                                type="date"
                                                value={data.date_naissance}
                                                onChange={(e) => setData('date_naissance', e.target.value)}
                                                className={errors?.date_naissance ? 'field-error' : ''}
                                            />
                                            {errors?.date_naissance && (
                                                <p className="error-message">{errors.date_naissance}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="pays_citoyennete">Pays de citoyenneté *</Label>
                                            <div className={errors?.pays_citoyennete ? 'select-error' : ''}>
                                                <Select value={data.pays_citoyennete} onValueChange={(value) => setData('pays_citoyennete', value)}>
                                                    <SelectTrigger className={errors?.pays_citoyennete ? 'field-error' : ''}>
                                                        <SelectValue placeholder="Sélectionnez votre pays de citoyenneté" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(countries).map(([code, name]) => (
                                                            <SelectItem key={code} value={code}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            {errors?.pays_citoyennete && (
                                                <p className="error-message">{errors.pays_citoyennete}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pays_naissance">Pays de naissance *</Label>
                                            <div className={errors?.pays_naissance ? 'select-error' : ''}>
                                                <Select value={data.pays_naissance} onValueChange={(value) => setData('pays_naissance', value)}>
                                                    <SelectTrigger className={errors?.pays_naissance ? 'field-error' : ''}>
                                                        <SelectValue placeholder="Sélectionnez votre pays de naissance" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(countries).map(([code, name]) => (
                                                            <SelectItem key={code} value={code}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            {errors?.pays_naissance && (
                                                <p className="error-message">{errors.pays_naissance}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Adresse */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center gap-2">
                                        <MapPin className="w-4 h-4" />
                                        Adresse de résidence
                                    </h3>

                                    <div className="grid gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="adresse_ligne1">Adresse ligne 1 *</Label>
                                            <Input
                                                id="adresse_ligne1"
                                                value={data.adresse_ligne1}
                                                onChange={(e) => setData('adresse_ligne1', e.target.value)}
                                                placeholder="Numéro et nom de rue"
                                                className={errors?.adresse_ligne1 ? 'field-error' : ''}
                                            />
                                            {errors?.adresse_ligne1 && (
                                                <p className="error-message">{errors.adresse_ligne1}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="adresse_ligne2">Adresse ligne 2</Label>
                                            <Input
                                                id="adresse_ligne2"
                                                value={data.adresse_ligne2}
                                                onChange={(e) => setData('adresse_ligne2', e.target.value)}
                                                placeholder="Complément d'adresse (optionnel)"
                                            />
                                        </div>

                                        <div className="grid md:grid-cols-3 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="ville">Ville *</Label>
                                                <Input
                                                    id="ville"
                                                    value={data.ville}
                                                    onChange={(e) => setData('ville', e.target.value)}
                                                    placeholder="Votre ville"
                                                    className={errors?.ville ? 'field-error' : ''}
                                                />
                                                {errors?.ville && (
                                                    <p className="error-message">{errors.ville}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="region">Région/État</Label>
                                                <Input
                                                    id="region"
                                                    value={data.region}
                                                    onChange={(e) => setData('region', e.target.value)}
                                                    placeholder="Région ou état"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="code_postal">Code postal</Label>
                                                <Input
                                                    id="code_postal"
                                                    value={data.code_postal}
                                                    onChange={(e) => setData('code_postal', e.target.value)}
                                                    placeholder="Code postal"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pays_adresse">Pays de résidence *</Label>
                                            <div className={errors?.pays_adresse ? 'select-error' : ''}>
                                                <Select value={data.pays_adresse} onValueChange={(value) => setData('pays_adresse', value)}>
                                                    <SelectTrigger className={errors?.pays_adresse ? 'field-error' : ''}>
                                                        <SelectValue placeholder="Sélectionnez votre pays de résidence" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Object.entries(countries).map(([code, name]) => (
                                                            <SelectItem key={code} value={code}>
                                                                {name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            {errors?.pays_adresse && (
                                                <p className="error-message">{errors.pays_adresse}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Contact */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center gap-2">
                                        <Phone className="w-4 h-4" />
                                        Contact
                                    </h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="telephone_verification">Téléphone de vérification *</Label>
                                        <Input
                                            id="telephone_verification"
                                            value={data.telephone_verification}
                                            onChange={(e) => setData('telephone_verification', e.target.value)}
                                            placeholder="Ex: +237 6XX XXX XXX"
                                            className={errors?.telephone_verification ? 'field-error' : ''}
                                        />
                                        {errors?.telephone_verification && (
                                            <p className="error-message">{errors.telephone_verification}</p>
                                        )}
                                        <p className="text-sm text-muted-foreground">
                                            Ce numéro sera utilisé pour vérifier votre identité
                                        </p>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.welcome')}>
                                            Retour
                                        </a>
                                    </Button>

                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing ? 'Enregistrement...' : 'Continuer'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
