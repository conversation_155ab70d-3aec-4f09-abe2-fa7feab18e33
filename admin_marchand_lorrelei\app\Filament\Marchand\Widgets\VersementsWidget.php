<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\Versement;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class VersementsWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 4;

    protected static ?string $heading = 'Versements récents';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Versement::query()
                    ->where('marchand_id', auth()->user()->marchands->first()->id ?? null)
                    ->latest('created_at')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('reference_versement')
                    ->label('Référence')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Référence copiée!')
                    ->copyMessageDuration(1500),
                
                Tables\Columns\TextColumn::make('montant_net')
                    ->label('Montant net')
                    ->money('FCFA')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('frais_transaction')
                    ->label('Frais')
                    ->money('FCFA')
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'warning' => 'EnAttente',
                        'primary' => 'EnCours',
                        'success' => 'Complété',
                        'danger' => 'Échoué',
                    ])
                    ->icons([
                        'heroicon-m-clock' => 'EnAttente',
                        'heroicon-m-arrow-path' => 'EnCours',
                        'heroicon-m-check-circle' => 'Complété',
                        'heroicon-m-x-circle' => 'Échoué',
                    ]),
                
                Tables\Columns\TextColumn::make('methode_versement')
                    ->label('Méthode')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'virement_bancaire' => 'Virement',
                        'orange_money' => 'Orange Money',
                        'mtn_money' => 'MTN Money',
                        'paypal' => 'PayPal',
                        'stripe' => 'Stripe',
                        'wave' => 'Wave',
                        default => $state
                    })
                    ->colors([
                        'primary' => 'virement_bancaire',
                        'warning' => ['orange_money', 'mtn_money'],
                        'info' => ['paypal', 'stripe'],
                        'success' => 'wave',
                    ]),
                
                Tables\Columns\TextColumn::make('date_demande')
                    ->label('Demandé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('date_completion')
                    ->label('Complété le')
                    ->dateTime('d/m/Y H:i')
                    ->placeholder('En attente')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('nombre_commandes')
                    ->label('Commandes')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCours' => 'En cours',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                    ]),
                
                Tables\Filters\SelectFilter::make('methode_versement')
                    ->label('Méthode')
                    ->options([
                        'virement_bancaire' => 'Virement bancaire',
                        'orange_money' => 'Orange Money',
                        'mtn_money' => 'MTN Money',
                        'paypal' => 'PayPal',
                        'stripe' => 'Stripe',
                        'wave' => 'Wave',
                    ]),
                
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        \Filament\Forms\Components\DatePicker::make('created_from')
                            ->label('Du'),
                        \Filament\Forms\Components\DatePicker::make('created_until')
                            ->label('Au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\Action::make('details')
                    ->label('Détails')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->modalHeading(fn (Versement $record): string => "Versement {$record->reference_versement}")
                    ->modalContent(fn (Versement $record): \Illuminate\Contracts\View\View => view('filament.modals.versement-details', ['versement' => $record]))
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Fermer'),
                
                Tables\Actions\Action::make('download_receipt')
                    ->label('Reçu')
                    ->icon('heroicon-m-document-arrow-down')
                    ->color('success')
                    ->visible(fn (Versement $record): bool => $record->statut === 'Complété')
                    ->url(fn (Versement $record): string => route('marchand.versements.receipt', $record))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('export')
                        ->label('Exporter')
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function ($records) {
                            // Logique d'export
                            return response()->download(
                                storage_path('app/exports/versements.csv')
                            );
                        }),
                ]),
            ])
            ->emptyStateHeading('Aucun versement')
            ->emptyStateDescription('Vos versements apparaîtront ici une fois que vous aurez des commandes livrées.')
            ->emptyStateIcon('heroicon-o-banknotes')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }
}
