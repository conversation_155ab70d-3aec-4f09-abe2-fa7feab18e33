import { useState } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { ImageModal } from '@/components/ui/ImageModal';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/Components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    ArrowLeft,
    MessageSquare,
    Send,
    Upload,
    X,
    Store,
    Package,
    Calendar,
    User,
    Clock,
    CheckCircle,
    Download,
    XCircle,
    RotateCcw,
    AlertTriangle,
    ChevronDown,
    ChevronUp,
    Info,
    ImageIcon,
    PaperclipIcon,
    FileText
} from 'lucide-react';

interface ConversationMessage {
    id: string;
    auteur_type: string;
    auteur_nom: string;
    message: string;
    type_message: string;
    pieces_jointes: Array<{
        nom_original: string;
        chemin: string;
        taille: number;
    }>;
    created_at: string;
}

interface Conversation {
    id: string;
    sujet: string;
    statut: string;
    statut_formate: string;
    statut_color: string;
    type_conversation: string;
    type_formate: string;
    priorite_formate: string;
    priorite_color: string;
    date_creation: string;
    date_dernier_message?: string;
    date_fermeture?: string;
    nombre_messages_total: number;
    messages_non_lus_client: number;
    marchand: {
        nomEntreprise: string;
        logo?: string;
    };
    commande_principale?: {
        numero_commande: string;
        montant_total_ttc: number;
    };
    produit?: {
        nom: string;
        image_principale?: string;
    };
    messages: ConversationMessage[];
}

interface Props {
    conversation: Conversation;
    canAddMessage: boolean;
    canClose: boolean;
    canReopen: boolean;
}

export default function ConversationShow({ conversation, canAddMessage, canClose, canReopen }: Props) {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [showCloseConfirm, setShowCloseConfirm] = useState(false);
    const [showConversationInfo, setShowConversationInfo] = useState(false);
    const [imageModal, setImageModal] = useState<{
        isOpen: boolean;
        imageUrl: string;
        imageName: string;
        imageSize?: number;
        downloadUrl?: string;
    }>({
        isOpen: false,
        imageUrl: '',
        imageName: '',
        imageSize: 0,
        downloadUrl: ''
    });
    const { props } = usePage();
    const successMessage = (props as any).flash?.success;

    const { data, setData, post, processing, errors, reset } = useForm({
        message: '',
        pieces_jointes: [] as File[]
    });

    const { post: closeConversation, processing: closingConversation } = useForm();
    const { post: reopenConversation, processing: reopeningConversation } = useForm();

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        const validFiles = files.filter(file => {
            const isValidType = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'].includes(file.type);
            const isValidSize = file.size <= 5120 * 1024; // 5MB
            return isValidType && isValidSize;
        });

        if (selectedFiles.length + validFiles.length <= 3) {
            const newFiles = [...selectedFiles, ...validFiles];
            setSelectedFiles(newFiles);
            setData('pieces_jointes', newFiles);
        }
    };

    const removeFile = (index: number) => {
        const newFiles = selectedFiles.filter((_, i) => i !== index);
        setSelectedFiles(newFiles);
        setData('pieces_jointes', newFiles);
    };

    // Fonctions pour le modal d'images
    const openImageModal = (attachment: any, message: any, attachmentIndex: number) => {
        const isImage = attachment.nom_original.match(/\.(jpg|jpeg|png|gif|webp)$/i);
        const imageUrl = isImage ? `/storage/${attachment.chemin}` : '';
        const downloadUrl = route('client.conversations.download', {
            messageId: message.id,
            attachmentIndex: attachmentIndex
        });

        setImageModal({
            isOpen: true,
            imageUrl,
            imageName: attachment.nom_original,
            imageSize: attachment.taille,
            downloadUrl
        });
    };

    const closeImageModal = () => {
        setImageModal({
            isOpen: false,
            imageUrl: '',
            imageName: '',
            imageSize: 0,
            downloadUrl: ''
        });
    };

    console.log("conversation ", conversation)
    const handleSubmitMessage = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('client.conversations.add-message', conversation.id), {
            forceFormData: true,
            onSuccess: () => {
                reset();
                setSelectedFiles([]);
            }
        });
    };

    const handleClose = () => {
        closeConversation(route('client.conversations.close', conversation.id), {
            onSuccess: () => {
                setShowCloseConfirm(false);
            }
        });
    };

    const handleReopen = () => {
        reopenConversation(route('client.conversations.reopen', conversation.id));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatAmount = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    };

    const getAuthorIcon = (type: string) => {
        switch (type) {
            case 'client':
                return <User className="h-4 w-4" />;
            case 'marchand':
                return <Store className="h-4 w-4" />;
            case 'system':
                return <Clock className="h-4 w-4" />;
            default:
                return <MessageSquare className="h-4 w-4" />;
        }
    };



    return (
        <ClientDashboardLayout>
            <Head title={`Conversation - ${conversation.sujet}`} />

            {/* Layout responsive optimisé */}
            <div className="h-screen flex flex-col">
                {/* Message de succès */}
                {successMessage && (
                    <Alert className="mx-4 mt-4">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            {successMessage}
                        </AlertDescription>
                    </Alert>
                )}

                {/* Header compact */}
                <div className="flex-shrink-0 p-4 border-b bg-white dark:bg-gray-800">
                    <div className="flex items-center gap-4">
                        <Link href={route('client.conversations.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Retour
                            </Button>
                        </Link>

                        {/* Info compacte mobile / normale desktop */}
                        <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                                <h1 className="text-lg md:text-2xl font-bold text-gray-900 dark:text-white">
                                    {conversation.sujet}
                                </h1>
                                <Badge variant={conversation.statut_color as any} className="text-xs status-badge">
                                    {conversation.statut_formate}
                                </Badge>
                                <Badge variant={conversation.priorite_color as any} className="text-xs status-badge">
                                    {conversation.priorite_formate}
                                </Badge>

                                {/* Bouton info mobile */}
                                <button
                                    onClick={() => setShowConversationInfo(!showConversationInfo)}
                                    className="md:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                    {showConversationInfo ? <ChevronUp className="h-4 w-4" /> : <Info className="h-4 w-4" />}
                                </button>
                            </div>

                            <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 truncate">
                                Conversation avec <span className="font-bold text-green-500 bg-green-100 dark:bg-green-900/20 dark:text-green-100 p-1 rounded-md">{conversation.marchand.nomEntreprise}</span>
                            </p>

                            {/* Info mobile déroulante */}
                            {showConversationInfo && (
                                <div className="md:hidden mt-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Type:</span>
                                        <span className="font-medium">{conversation.type_conversation}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Messages:</span>
                                        <span>{conversation.nombre_messages_total}</span>
                                    </div>
                                    {conversation.commande_principale && (
                                        <div className="flex justify-between">
                                            <span className="text-gray-500">Commande:</span>
                                            <span className="font-medium">{conversation.commande_principale.numero_commande}</span>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Actions desktop */}
                        <div className="hidden md:flex gap-2">
                            {canReopen && (
                                <Button
                                    variant="outline"
                                    onClick={handleReopen}
                                    disabled={reopeningConversation}
                                    className="text-green-600 hover:text-green-700"
                                    size="sm"
                                >
                                    <RotateCcw className="h-4 w-4 mr-2" />
                                    {reopeningConversation ? 'Réouverture...' : 'Rouvrir'}
                                </Button>
                            )}
                            {canClose && (
                                <Button
                                    variant="outline"
                                    onClick={() => setShowCloseConfirm(true)}
                                    className="text-red-600 hover:text-red-700"
                                    size="sm"
                                >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Fermer
                                </Button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Layout principal : Chat + Sidebar */}
                <div className="flex-1 flex gap-4 p-4 min-h-0">
                    {/* Zone de chat principale */}
                    <div className="flex-1 flex flex-col min-w-0">
                        <Card className="flex flex-col h-full">
                            <CardHeader className="flex-shrink-0 border-b">
                                <CardTitle className="flex items-center gap-2">
                                    <MessageSquare className="h-5 w-5" />
                                    Messages ({conversation.messages.length})
                                </CardTitle>
                            </CardHeader>

                            {/* Zone des messages avec scroll optimisé */}
                            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                                {conversation.messages.length === 0 ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="text-center">
                                            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                                            <p className="text-gray-500">Aucun message pour le moment</p>
                                            <p className="text-sm text-gray-400 mt-1">Commencez la conversation avec le marchand</p>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        {conversation.messages.map((message) => (
                                            <div
                                                key={message.id}
                                                className={`flex ${message.auteur_type === 'client' ? 'justify-end' : 'justify-start'} chat-bubble new-message`}
                                            >
                                                <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md ${
                                                    message.auteur_type === 'client'
                                                        ? 'bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 text-white'
                                                        : message.auteur_type === 'marchand'
                                                        ? 'bg-green-100 dark:bg-green-900/20 text-green-900 dark:text-green-100 border border-green-200 dark:border-green-800'
                                                        : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                                                }`}>

                                                    {message.auteur_type !== 'client' && (
                                                        <div className="flex items-center gap-2 mb-2">
                                                            {getAuthorIcon(message.auteur_type)}
                                                            <span className="text-sm font-medium">{message.auteur_nom}</span>
                                                            {message.type_message !== 'message' && (
                                                                <Badge variant="outline" className="text-xs">
                                                                    {message.type_message}
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    )}

                                                    <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                                        {message.message}
                                                    </p>

                                                    {message.pieces_jointes && message.pieces_jointes.length > 0 && (
                                                        <div className="mt-3 space-y-2">
                                                            {message.pieces_jointes.map((file, index) => {
                                                                const isImage = file.nom_original.match(/\.(jpg|jpeg|png|gif|webp)$/i);
                                                                return (
                                                                    <div key={index} className="flex items-center gap-2 p-2 bg-blue-900/20 rounded text-xs cursor-pointer ">
                                                                        {isImage ? (
                                                                            <ImageIcon className="h-3 w-3 text-blue-400" />
                                                                        ) : (
                                                                            <FileText className="h-3 w-3" />
                                                                        )}
                                                                        <button
                                                                            onClick={() => openImageModal(file, message, index)}
                                                                            className="truncate flex-1 text-left hover:underline cursor-pointer"
                                                                        >
                                                                            {file.nom_original}
                                                                        </button>
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            className="h-6 w-6 p-0 hover:bg-black/20"
                                                                            onClick={() => openImageModal(file, message, index)}
                                                                        >
                                                                            <Download className="h-3 w-3" />
                                                                        </Button>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    )}

                                                    <div className={`text-xs mt-2 ${
                                                        message.auteur_type === 'client' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                                                    }`}>
                                                        {formatDate(message.created_at)}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}

                                        {/* Auto-scroll anchor */}
                                        <div id="messages-end" />
                                    </>
                                )}
                            </div>

                            {/* Zone de saisie attractive */}
                            {canAddMessage && (
                                <div className="flex-shrink-0 border-t bg-white dark:bg-gray-800 p-4">
                                    <form onSubmit={handleSubmitMessage} className="space-y-3">
                                        {/* Fichiers sélectionnés avec design amélioré */}
                                        {selectedFiles.length > 0 && (
                                            <div className="flex flex-wrap gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                                <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300 mb-2 w-full">
                                                    <PaperclipIcon className="h-4 w-4" />
                                                    <span className="font-medium">Fichiers joints ({selectedFiles.length}/3)</span>
                                                </div>
                                                {selectedFiles.map((file, index) => (
                                                    <div key={index} className="flex items-center gap-2 bg-white dark:bg-gray-800 rounded-lg px-3 py-2 border shadow-sm file-attachment">
                                                        <ImageIcon className="h-4 w-4 text-blue-500" />
                                                        <span className="text-sm truncate max-w-32">{file.name}</span>
                                                        <span className="text-xs text-gray-500">
                                                            {(file.size / 1024).toFixed(0)}KB
                                                        </span>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeFile(index)}
                                                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-colors cursor-pointer"
                                                        >
                                                            <X className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        <div className="flex gap-3">
                                            <div className="flex-1">
                                                <Textarea
                                                    value={data.message}
                                                    onChange={(e) => setData('message', e.target.value)}
                                                    placeholder="Tapez votre message..."
                                                    rows={3}
                                                    maxLength={2000}
                                                    className="resize-none border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 chat-input"
                                                />
                                                <div className="flex justify-between items-center mt-1">
                                                    <span className="text-xs text-gray-500">
                                                        {data.message.length}/2000 caractères
                                                    </span>
                                                    {errors.message && (
                                                        <span className="text-xs text-red-600">{errors.message}</span>
                                                    )}
                                                </div>
                                            </div>

                                            <div className="flex flex-col gap-2">
                                                {/* Zone d'upload attractive */}
                                                <input
                                                    type="file"
                                                    multiple
                                                    accept=".jpg,.jpeg,.png,.pdf"
                                                    onChange={handleFileUpload}
                                                    className="hidden"
                                                    id="message-file-upload"
                                                />
                                                <Label htmlFor="message-file-upload" className="cursor-pointer">
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        className=" relative h-10 w-10 p-0 border-dashed border-2 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 upload-zone cursor-pointer"
                                                        disabled={selectedFiles.length >= 3}
                                                    >
                                                        <input
                                                            type="file"
                                                            multiple
                                                            accept=".jpg,.jpeg,.png,.pdf"
                                                            onChange={handleFileUpload}
                                                            className="opacity-0 absolute top-0 left-0 w-full h-full"
                                                            id="message-file-upload"
                                                        />
                                                        <Upload className="h-4 w-4" />
                                                    </Button>
                                                </Label>

                                                <Button
                                                    type="submit"
                                                    disabled={processing || !data.message.trim()}
                                                    size="sm"
                                                    className="h-10 w-10 p-0"
                                                >
                                                    {processing ? (
                                                        <Clock className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <Send className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </Card>
                    </div>

                    {/* Sidebar informations (desktop uniquement) */}
                    <div className="hidden md:block w-80 flex-shrink-0">
                        <Card className="h-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-lg">
                                    <Info className="h-5 w-5" />
                                    Informations
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Marchand</Label>
                                        <div className="flex items-center gap-2 mt-1">
                                            <Store className="h-4 w-4 text-gray-400" />
                                            <span className="font-medium text-green-500 bg-green-100 dark:bg-green-900/20  p-1 rounded-md">{conversation.marchand.nomEntreprise}</span>
                                        </div>
                                    </div>

                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Type de conversation</Label>
                                        <p className="mt-1 font-medium">{conversation.type_conversation}</p>
                                    </div>

                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Date de création</Label>
                                        <div className="flex items-center gap-2 mt-1">
                                            <Calendar className="h-4 w-4 text-gray-400" />
                                            <span>{formatDate(conversation.date_creation)}</span>
                                        </div>
                                    </div>

                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Messages</Label>
                                        <p className="mt-1 font-medium">{conversation.nombre_messages_total} message{conversation.nombre_messages_total > 1 ? 's' : ''}</p>
                                    </div>
                                </div>

                                {conversation.commande_principale && (
                                    <div className="border-t pt-4">
                                        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                            <Label className="text-sm font-medium text-blue-700 dark:text-blue-300">Commande liée</Label>
                                            <div className="flex items-center gap-2 mt-1">
                                                <Package className="h-4 w-4 text-blue-500" />
                                                <span className="font-medium">{conversation.commande_principale.numero_commande}</span>
                                            </div>
                                            <span className="text-sm text-blue-600 dark:text-blue-400">
                                                {formatAmount(conversation.commande_principale.montant_total_ttc)}
                                            </span>
                                        </div>
                                    </div>
                                )}

                                {conversation.produit && (
                                    <div className="border-t pt-4">
                                        <Label className="text-sm font-medium text-gray-500">Produit concerné</Label>
                                        <p className="mt-2 text-sm text-gray-900 dark:text-white leading-relaxed">{conversation.produit.nom}</p>
                                    </div>
                                )}

                                {conversation.date_fermeture && (
                                    <div className="border-t pt-4">
                                        <Label className="text-sm font-medium text-gray-500">Date de fermeture</Label>
                                        <p className="mt-2 text-sm text-gray-900 dark:text-white">{formatDate(conversation.date_fermeture)}</p>
                                    </div>
                                )}

                                {/* Actions */}
                                <div className="border-t pt-4 space-y-2">
                                    {canReopen && (
                                        <Button
                                            variant="outline"
                                            onClick={handleReopen}
                                            disabled={reopeningConversation}
                                            className="w-full text-green-600 hover:text-green-700 hover:bg-green-50"
                                        >
                                            <RotateCcw className="h-4 w-4 mr-2" />
                                            {reopeningConversation ? 'Réouverture...' : 'Rouvrir'}
                                        </Button>
                                    )}
                                    {canClose && (
                                        <Button
                                            variant="outline"
                                            onClick={() => setShowCloseConfirm(true)}
                                            className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                                        >
                                            <XCircle className="h-4 w-4 mr-2" />
                                            Fermer la conversation
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Suggestion de créer un litige */}
                {conversation.statut === 'fermee' && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <div className="space-y-2">
                                <p>Cette conversation est fermée. Si votre problème n'est pas résolu, vous pouvez créer un litige officiel.</p>
                                <Link href={route('client.disputes.create')}>
                                    <Button variant="outline" size="sm">
                                        Créer un litige
                                    </Button>
                                </Link>
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Modal d'image */}


                {/* Confirmation de fermeture */}
                {showCloseConfirm && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <div className="space-y-3">
                                <p>Êtes-vous sûr de vouloir fermer cette conversation ? Vous pourrez la rouvrir dans les 7 jours.</p>
                                <div className="flex gap-2">
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={handleClose}
                                        disabled={closingConversation}
                                    >
                                        {closingConversation ? 'Fermeture...' : 'Confirmer la fermeture'}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setShowCloseConfirm(false)}
                                    >
                                        Annuler
                                    </Button>
                                </div>
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Modal d'images */}
                <ImageModal
                    isOpen={imageModal.isOpen}
                    onClose={closeImageModal}
                    imageUrl={imageModal.imageUrl}
                    imageName={imageModal.imageName}
                    imageSize={imageModal.imageSize}
                    downloadUrl={imageModal.downloadUrl}
                />
            </div>
        </ClientDashboardLayout>
    );
}
