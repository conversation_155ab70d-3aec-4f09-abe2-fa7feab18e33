<?php

namespace App\Filament\Marchand\Resources\AbonnementResource\Pages;

use App\Filament\Marchand\Resources\AbonnementResource;
use Filament\Resources\Pages\ListRecords;

class ListAbonnements extends ListRecords
{
    protected static string $resource = AbonnementResource::class;

    protected function getTableQuery(): ?\Illuminate\Database\Eloquent\Builder
    {
        // Filtrer pour n'afficher que les abonnements du marchand connecté
        $marchand = auth()->user()->marchand;
        // dd($marchand);
        return parent::getTableQuery()?->where('marchand_id', $marchand?->id);

    }
}
