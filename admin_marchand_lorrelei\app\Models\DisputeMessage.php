<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class DisputeMessage extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'dispute_messages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dispute_id',
        'auteur_type',
        'auteur_id',
        'auteur_nom',
        'message',
        'type_message',
        'pieces_jointes',
        'interne',
        'lu_par_client',
        'lu_par_admin',
        'lu_par_marchand',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'pieces_jointes' => 'array',
        'interne' => 'boolean',
        'lu_par_client' => 'boolean',
        'lu_par_admin' => 'boolean',
        'lu_par_marchand' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type_message' => 'message',
        'interne' => false,
        'lu_par_client' => false,
        'lu_par_admin' => false,
        'lu_par_marchand' => false,
    ];

    /**
     * Relation avec le litige
     */
    public function dispute(): BelongsTo
    {
        return $this->belongsTo(Dispute::class);
    }

    /**
     * Relation avec l'admin auteur
     */
    public function auteurAdmin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'auteur_id')->where('auteur_type', 'admin');
    }

    /**
     * Relation avec le marchand auteur (via admin_marchand_lorrelei)
     */
    public function auteurMarchand(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Marchand::class, 'auteur_id')->where('auteur_type', 'marchand');
    }

    /**
     * Marque le message comme lu par un type d'utilisateur
     */
    public function marquerCommeLu(string $typeUtilisateur): void
    {
        $champ = match($typeUtilisateur) {
            'client' => 'lu_par_client',
            'admin' => 'lu_par_admin',
            'marchand' => 'lu_par_marchand',
            default => null
        };

        if ($champ) {
            $this->update([$champ => true]);
        }
    }

    /**
     * Scope pour les messages publics
     */
    public function scopePublics($query)
    {
        return $query->where('interne', false);
    }

    /**
     * Scope pour les messages internes
     */
    public function scopeInternes($query)
    {
        return $query->where('interne', true);
    }

    /**
     * Scope pour les messages non lus par un type d'utilisateur
     */
    public function scopeNonLusPar($query, string $typeUtilisateur)
    {
        $champ = match($typeUtilisateur) {
            'client' => 'lu_par_client',
            'admin' => 'lu_par_admin',
            'marchand' => 'lu_par_marchand',
            default => null
        };

        if ($champ) {
            return $query->where($champ, false);
        }

        return $query;
    }

    /**
     * Obtient le type de message formaté
     */
    public function getTypeMessageFormateAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'Message',
            'changement_statut' => 'Changement de statut',
            'piece_jointe' => 'Pièce jointe',
            'solution_proposee' => 'Solution proposée',
            'escalade' => 'Escalade',
            'resolution' => 'Résolution',
            'system' => 'Système',
            default => 'Message'
        };
    }

    /**
     * Obtient l'icône selon le type de message
     */
    public function getIconeAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'heroicon-m-chat-bubble-left',
            'changement_statut' => 'heroicon-m-arrow-path',
            'piece_jointe' => 'heroicon-m-paper-clip',
            'solution_proposee' => 'heroicon-m-light-bulb',
            'escalade' => 'heroicon-m-arrow-trending-up',
            'resolution' => 'heroicon-m-check-circle',
            'system' => 'heroicon-m-cog',
            default => 'heroicon-m-chat-bubble-left'
        };
    }

    /**
     * Obtient la couleur selon le type d'auteur
     */
    public function getAuteurColorAttribute(): string
    {
        return match($this->auteur_type) {
            'client' => 'primary',
            'admin' => 'success',
            'marchand' => 'warning',
            'system' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Obtient le badge de l'auteur
     */
    public function getAuteurBadgeAttribute(): string
    {
        return match($this->auteur_type) {
            'client' => 'Client',
            'admin' => 'Support',
            'marchand' => 'Marchand',
            'system' => 'Système',
            default => ucfirst($this->auteur_type)
        };
    }

    /**
     * Vérifie si le message a des pièces jointes
     */
    public function getAPiecesJointesAttribute(): bool
    {
        return !empty($this->pieces_jointes) && is_array($this->pieces_jointes);
    }

    /**
     * Obtient le nombre de pièces jointes
     */
    public function getNombrePiecesJointesAttribute(): int
    {
        return $this->a_pieces_jointes ? count($this->pieces_jointes) : 0;
    }

    /**
     * Vérifie si le message est récent (moins de 24h)
     */
    public function getEstRecentAttribute(): bool
    {
        return $this->created_at->isAfter(now()->subDay());
    }

    /**
     * Obtient le temps écoulé depuis la création
     */
    public function getTempsEcouleAttribute(): string
    {
        $diff = $this->created_at->diffForHumans();
        return $diff;
    }
}
