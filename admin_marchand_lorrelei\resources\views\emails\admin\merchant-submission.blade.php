<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle soumission marchand - Lorrel<PERSON> Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .alert-badge {
            display: inline-block;
            padding: 8px 16px;
            background-color: #fef3c7;
            color: #92400e;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 10px 0;
        }
        .content {
            margin: 20px 0;
        }
        .merchant-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3b82f6;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-label {
            font-weight: 600;
            color: #374151;
            min-width: 120px;
        }
        .info-value {
            color: #1f2937;
            flex: 1;
            text-align: right;
        }
        .priority-high {
            background-color: #fee2e2;
            border-left-color: #dc2626;
        }
        .priority-medium {
            background-color: #fef3c7;
            border-left-color: #f59e0b;
        }
        .priority-low {
            background-color: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #dc2626;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            font-size: 16px;
        }
        .button:hover {
            background-color: #b91c1c;
        }
        .actions {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .action-item {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        .action-item::before {
            content: "⚡";
            position: absolute;
            left: 0;
            color: #dc2626;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .urgent {
            background-color: #fee2e2;
            border: 1px solid #fecaca;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .urgent-text {
            color: #991b1b;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔔 Lorrelei Admin</div>
            <h1>Nouvelle soumission marchand</h1>
            <div class="alert-badge">Action requise</div>
        </div>

        <div class="content">
            <p>Bonjour <strong>{{ $admin_name }}</strong>,</p>

            <p>Une nouvelle soumission de marchand nécessite votre attention et doit être validée.</p>

            @php
                $submittedDays = \Carbon\Carbon::parse($submitted_at)->diffInDays(now());
                $priorityClass = $submittedDays >= 2 ? 'priority-high' : ($submittedDays >= 1 ? 'priority-medium' : 'priority-low');
            @endphp

            <div class="merchant-info {{ $priorityClass }}">
                <h3 style="margin-top: 0; color: #1f2937;">📋 Détails du marchand</h3>
                
                <div class="info-row">
                    <span class="info-label">👤 Nom :</span>
                    <span class="info-value"><strong>{{ $user_name }}</strong></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">📧 Email :</span>
                    <span class="info-value">{{ $user_email }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">🏢 Entreprise :</span>
                    <span class="info-value"><strong>{{ $business_name }}</strong></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">🏷️ Type :</span>
                    <span class="info-value">{{ ucfirst($business_type ?? 'Non spécifié') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">📅 Soumis le :</span>
                    <span class="info-value">{{ \Carbon\Carbon::parse($submitted_at)->format('d/m/Y à H:i') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">⏱️ En attente depuis :</span>
                    <span class="info-value">
                        @if($submittedDays >= 2)
                            <span style="color: #dc2626; font-weight: 600;">{{ $submittedDays }} jours ⚠️</span>
                        @elseif($submittedDays >= 1)
                            <span style="color: #f59e0b; font-weight: 600;">{{ $submittedDays }} jour(s)</span>
                        @else
                            <span style="color: #10b981;">Moins d'un jour</span>
                        @endif
                    </span>
                </div>
            </div>

            @if($submittedDays >= 2)
            <div class="urgent">
                <div class="urgent-text">⚠️ URGENT : Cette demande est en attente depuis plus de 2 jours !</div>
                <p style="margin: 5px 0 0 0; color: #7f1d1d;">
                    Merci de traiter cette demande en priorité pour respecter nos délais de service.
                </p>
            </div>
            @endif

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ $action_url }}" class="button">
                    🔍 Examiner la soumission
                </a>
            </div>

            <div class="actions">
                <h3 style="margin-top: 0; color: #374151;">⚡ Actions disponibles :</h3>
                <div class="action-item">Examiner le dossier complet</div>
                <div class="action-item">Vérifier les documents fournis</div>
                <div class="action-item">Approuver ou rejeter la demande</div>
                <div class="action-item">Envoyer une notification automatique au marchand</div>
            </div>

            <p><strong>Objectif de traitement :</strong> 24-48h maximum pour maintenir la qualité de service.</p>
        </div>

        <div class="footer">
            <p style="color: #dc2626; font-weight: 600;">
                Dashboard Admin Lorrelei 🛡️
            </p>
            <p>
                Cet email a été envoyé automatiquement lors d'une nouvelle soumission marchand.<br>
                Pour toute question technique, contactez l'équipe développement.
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                Email généré le {{ now()->format('d/m/Y à H:i:s') }}
            </p>
        </div>
    </div>
</body>
</html>
