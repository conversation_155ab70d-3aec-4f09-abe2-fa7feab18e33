<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class AdressesRelationManager extends RelationManager
{
    protected static string $relationship = 'adresses';

    protected static ?string $recordTitleAttribute = 'type';

    protected static ?string $title = 'Adresses';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('type')
                    ->label('Type')
                    ->options([
                        'Livraison' => 'Livraison',
                        'Facturation' => 'Facturation',
                        'Entreprise' => 'Entreprise',
                    ])
                    ->required(),

                Forms\Components\Textarea::make('rue')
                    ->label('Rue')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\TextInput::make('code_postal')
                            ->label('Code postal')
                            ->required(),

                        Forms\Components\TextInput::make('ville')
                            ->label('Ville')
                            ->required(),

                        Forms\Components\TextInput::make('etat')
                            ->label('État/Province'),

                        Forms\Components\TextInput::make('pays')
                            ->label('Pays')
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Toggle::make('is_default')
                    ->label('Adresse par défaut')
                    ->default(false),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Livraison' => 'success',
                        'Facturation' => 'warning',
                        'Entreprise' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('rue')
                    ->label('Rue')
                    ->limit(30),

                Tables\Columns\TextColumn::make('code_postal')
                    ->label('Code postal'),

                Tables\Columns\TextColumn::make('ville')
                    ->label('Ville'),

                Tables\Columns\TextColumn::make('pays')
                    ->label('Pays'),

                Tables\Columns\IconColumn::make('is_default')
                    ->label('Par défaut')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'Livraison' => 'Livraison',
                        'Facturation' => 'Facturation',
                        'Entreprise' => 'Entreprise',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
