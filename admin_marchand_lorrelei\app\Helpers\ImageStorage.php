<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageStorage
{
    /**
     * Détermine le dossier de stockage basé sur l'ID
     * 
     * @param int|string $id L'identifiant
     * @return string Le préfixe du dossier
     */
    public static function getFolderPrefix($id): string
    {
        $idInt = (int)$id;
        return $idInt < 1000 ? '0' : substr((string)$idInt, 0, -3);
    }

    /**
     * Stocke une image dans le dossier approprié
     * 
     * @param UploadedFile $file Le fichier à stocker
     * @param int|string $id L'identifiant associé
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners')
     * @return string Le chemin relatif de l'image stockée
     */
    public static function storeImage(UploadedFile $file, $id, string $baseDir): string
    {
        $folderPrefix = self::getFolderPrefix($id);
        $directory = "{$baseDir}/{$folderPrefix}";
        
        // Créer le dossier s'il n'existe pas
        $fullPath = public_path("images/{$directory}");
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }
        
        // Générer un nom de fichier unique
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        
        // Déplacer le fichier
        $file->move($fullPath, $filename);
        
        // Retourner le chemin relatif
        return "{$directory}/{$filename}";
    }

    /**
     * Génère l'URL complète pour une image
     * 
     * @param string $path Le chemin relatif de l'image
     * @return string L'URL complète
     */
    public static function getImageUrl(string $path): string
    {
        return url("/images/{$path}");
    }
}
