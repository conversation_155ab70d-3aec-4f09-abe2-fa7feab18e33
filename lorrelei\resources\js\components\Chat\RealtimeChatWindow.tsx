import React, { useState, useEffect, useRef } from 'react';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface Message {
    id: string;
    message: string;
    auteur_type: string;
    auteur_nom: string;
    created_at: string;
    pieces_jointes?: string[];
}

interface RealtimeChatWindowProps {
    conversationId: string;
    initialMessages: Message[];
    currentUserId: string;
    currentUserType: 'client' | 'marchand' | 'admin';
    onSendMessage: (message: string, files?: File[]) => Promise<void>;
    className?: string;
}

export const RealtimeChatWindow: React.FC<RealtimeChatWindowProps> = ({
    conversationId,
    initialMessages,
    currentUserId,
    currentUserType,
    onSendMessage,
    className = '',
}) => {
    const [messages, setMessages] = useState<Message[]>(initialMessages);
    const [newMessage, setNewMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Hook WebSocket avec gestion automatique de la frappe
    const {
        isConnected,
        typingUsers,
        formatTypingUsers,
        useTypingInput,
        requestNotificationPermission,
    } = useReverbWebSocket({
        conversationId,
        userId: currentUserId,
        onMessage: (data) => {
            // Ajouter le nouveau message à la liste
            setMessages(prev => [...prev, data.message]);
            scrollToBottom();
        },
        onNotification: (data) => {
            console.log('Notification reçue:', data);
        },
    });

    // Hook pour gérer la frappe automatiquement
    const { handleInputChange, handleInputBlur } = useTypingInput();

    // Faire défiler vers le bas automatiquement
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Demander la permission pour les notifications au montage
    useEffect(() => {
        requestNotificationPermission();
    }, [requestNotificationPermission]);

    // Gérer les changements dans l'input
    const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setNewMessage(value);
        handleInputChange(value);
    };

    // Gérer l'envoi du message
    const handleSendMessage = async () => {
        if (!newMessage.trim() && selectedFiles.length === 0) return;

        setIsLoading(true);
        try {
            await onSendMessage(newMessage, selectedFiles);
            setNewMessage('');
            setSelectedFiles([]);
            handleInputBlur(); // Arrêter l'indicateur de frappe
        } catch (error) {
            console.error('Erreur envoi message:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Gérer l'appui sur Entrée
    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Gérer la sélection de fichiers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setSelectedFiles(prev => [...prev, ...files]);
    };

    // Supprimer un fichier sélectionné
    const removeFile = (index: number) => {
        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    };

    // Formater la date
    const formatMessageTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('fr-FR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    };

    // Filtrer les utilisateurs qui tapent (exclure l'utilisateur actuel)
    const otherTypingUsers = typingUsers.filter(user => user.user_id.toString() !== currentUserId);

    return (
        <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>
            {/* Header avec statut de connexion */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Conversation
                </h3>
                <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                        {isConnected ? 'En ligne' : 'Hors ligne'}
                    </span>
                </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`flex ${
                            message.auteur_type === currentUserType ? 'justify-end' : 'justify-start'
                        }`}
                    >
                        <div
                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.auteur_type === currentUserType
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                            }`}
                        >
                            {message.auteur_type !== currentUserType && (
                                <div className="text-sm font-medium mb-1 opacity-75">
                                    {message.auteur_nom}
                                </div>
                            )}
                            <div className="whitespace-pre-wrap">{message.message}</div>
                            {message.pieces_jointes && message.pieces_jointes.length > 0 && (
                                <div className="mt-2 space-y-1">
                                    {message.pieces_jointes.map((file, index) => (
                                        <div key={index} className="text-sm opacity-75">
                                            📎 {file}
                                        </div>
                                    ))}
                                </div>
                            )}
                            <div className="text-xs opacity-75 mt-1">
                                {formatMessageTime(message.created_at)}
                            </div>
                        </div>
                    </div>
                ))}
                
                {/* Indicateur "en train d'écrire" */}
                {otherTypingUsers.length > 0 && (
                    <div className="flex justify-start">
                        <div className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-4 py-2 rounded-lg text-sm animate-pulse">
                            <div className="flex items-center space-x-2">
                                <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span>{formatTypingUsers(otherTypingUsers)}</span>
                            </div>
                        </div>
                    </div>
                )}
                
                <div ref={messagesEndRef} />
            </div>

            {/* Fichiers sélectionnés */}
            {selectedFiles.length > 0 && (
                <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex flex-wrap gap-2">
                        {selectedFiles.map((file, index) => (
                            <div key={index} className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-1">
                                <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                                <button
                                    onClick={() => removeFile(index)}
                                    className="ml-2 text-red-500 hover:text-red-700"
                                >
                                    ×
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Zone de saisie */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
                <div className="flex space-x-2">
                    <div className="flex-1">
                        <textarea
                            value={newMessage}
                            onChange={handleMessageChange}
                            onKeyPress={handleKeyPress}
                            onBlur={handleInputBlur}
                            placeholder="Tapez votre message..."
                            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows={2}
                            disabled={isLoading}
                        />
                    </div>
                    <div className="flex flex-col space-y-2">
                        <button
                            onClick={() => fileInputRef.current?.click()}
                            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            title="Joindre un fichier"
                        >
                            📎
                        </button>
                        <button
                            onClick={handleSendMessage}
                            disabled={(!newMessage.trim() && selectedFiles.length === 0) || isLoading}
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? '...' : 'Envoyer'}
                        </button>
                    </div>
                </div>
                
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    className="hidden"
                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                />
            </div>
        </div>
    );
};
