<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MerchantValidation;
use App\Models\MerchantValidationDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class MerchantValidationController extends Controller
{
    /**
     * Télécharger un document de validation marchand
     */
    public function downloadDocument(MerchantValidation $validation, MerchantValidationDocument $document): BinaryFileResponse
    {
        // Vérifier que l'utilisateur est admin
        if (!Auth::user()->is_admin) {
            abort(403, 'Accès non autorisé');
        }

        // Vérifier que le document appartient bien à cette validation
        if ($document->merchant_validation_id !== $validation->id) {
            abort(404, 'Document non trouvé');
        }

        // Vérifier que le fichier existe
        if (!Storage::exists($document->file_path)) {
            abort(404, 'Fichier non trouvé sur le serveur');
        }

        // Télécharger le fichier avec le nom original
        return response()->download(
            Storage::path($document->file_path),
            $document->nom_fichier ?: $document->original_name,
            [
                'Content-Type' => $document->mime_type,
            ]
        );
    }

    /**
     * Prévisualiser un document (pour les images)
     */
    public function previewDocument(MerchantValidation $validation, MerchantValidationDocument $document)
    {
        // Vérifier que l'utilisateur est admin
        if (!Auth::user()->is_admin) {
            abort(403, 'Accès non autorisé');
        }

        // Vérifier que le document appartient bien à cette validation
        if ($document->merchant_validation_id !== $validation->id) {
            abort(404, 'Document non trouvé');
        }

        // Vérifier que le fichier existe
        if (!Storage::exists($document->file_path)) {
            abort(404, 'Fichier non trouvé sur le serveur');
        }

        // Vérifier que c'est une image
        if (!str_starts_with($document->mime_type, 'image/')) {
            abort(400, 'Ce fichier n\'est pas une image');
        }

        // Retourner l'image
        return response()->file(Storage::path($document->file_path), [
            'Content-Type' => $document->mime_type,
        ]);
    }

    /**
     * Télécharger tous les documents d'une validation en ZIP
     */
    public function downloadAllDocuments(MerchantValidation $validation)
    {
        // Vérifier que l'utilisateur est admin
        if (!Auth::user()->is_admin) {
            abort(403, 'Accès non autorisé');
        }

        $documents = $validation->documents;

        if ($documents->isEmpty()) {
            abort(404, 'Aucun document trouvé');
        }

        // Créer un fichier ZIP temporaire
        $zipFileName = 'documents_marchand_' . $validation->user->name . '_' . now()->format('Y-m-d_H-i-s') . '.zip';
        $zipPath = storage_path('app/temp/' . $zipFileName);

        // Créer le dossier temp s'il n'existe pas
        if (!file_exists(dirname($zipPath))) {
            mkdir(dirname($zipPath), 0755, true);
        }

        $zip = new \ZipArchive();
        if ($zip->open($zipPath, \ZipArchive::CREATE) !== TRUE) {
            abort(500, 'Impossible de créer le fichier ZIP');
        }

        foreach ($documents as $document) {
            if (Storage::exists($document->file_path)) {
                $fileName = $document->type_document . '_' . ($document->nom_fichier ?: $document->original_name);
                $zip->addFile(Storage::path($document->file_path), $fileName);
            }
        }

        $zip->close();

        // Télécharger le ZIP et le supprimer après
        return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
    }
}
