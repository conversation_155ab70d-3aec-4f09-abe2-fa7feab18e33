import React from 'react';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface ConnectionIndicatorProps {
    className?: string;
    showText?: boolean;
    size?: 'sm' | 'md' | 'lg';
}

export const ConnectionIndicator: React.FC<ConnectionIndicatorProps> = ({
    className = '',
    showText = true,
    size = 'md',
}) => {
    const { connectionStats } = useReverbWebSocket({});

    const sizeClasses = {
        sm: 'w-2 h-2',
        md: 'w-3 h-3',
        lg: 'w-4 h-4',
    };

    const textSizeClasses = {
        sm: 'text-xs',
        md: 'text-sm',
        lg: 'text-base',
    };

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <div
                className={`${sizeClasses[size]} rounded-full transition-colors duration-300 ${
                    connectionStats.connected
                        ? 'bg-green-500 shadow-green-500/50 shadow-sm'
                        : 'bg-red-500 shadow-red-500/50 shadow-sm'
                }`}
                title={connectionStats.connected ? 'Connecté aux WebSockets' : 'Déconnecté des WebSockets'}
            >
                {connectionStats.connected && (
                    <div className={`${sizeClasses[size]} rounded-full bg-green-400 animate-ping`}></div>
                )}
            </div>
            
            {showText && (
                <span className={`${textSizeClasses[size]} text-gray-600 dark:text-gray-400`}>
                    {connectionStats.connected ? 'En ligne' : 'Hors ligne'}
                    {connectionStats.activeChannels > 0 && (
                        <span className="ml-1 text-xs text-gray-500">
                            ({connectionStats.activeChannels} canal{connectionStats.activeChannels > 1 ? 'aux' : ''})
                        </span>
                    )}
                </span>
            )}
        </div>
    );
};
