import React, { useState, useEffect, useRef } from 'react';
import { Head } from '@inertiajs/react';
import {
    ExclamationTriangleIcon,
    UserIcon,
    MagnifyingGlassIcon,
    XMarkIcon,
    CheckIcon,
    ClockIcon,
    ShieldCheckIcon,
    UserGroupIcon,
    ArrowPathIcon,
    Bars3Icon,
    ShoppingCartIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import ThemeSelector from '@/components/ThemeSelector';
import AttachmentDisplay from '@/components/AttachmentDisplay';
import ImageModal, { useImageModal } from '@/components/ImageModal';
import { EnhancedDisputeChat } from '../../components/Disputes/EnhancedDisputeChat';
import { ConnectionIndicator } from '../../components/WebSocket/ConnectionIndicator';
import { NotificationToast } from '../../components/WebSocket/NotificationToast';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
}

interface Client {
    id: number;
    user: User;
}

interface Marchand {
    id: number;
    nom_entreprise: string;
    user?: User;
}

interface CommandePrincipale {
    id: number;
    numero_commande: string;
    montant_total_ttc: number;
    statut_global: string;
    date_commande: string;
    produits?: Array<{
        id: number;
        nom: string;
        prix_unitaire: number;
        quantite: number;
        image_principale?: string;
    }>;
}

interface Dispute {
    id: number;
    numero_litige: string;
    type_litige: string;
    description: string;
    statut: 'ouvert' | 'en_cours' | 'attente_client' | 'attente_marchand' | 'escalade' | 'resolu' | 'ferme' | 'annule';
    priorite: 'basse' | 'normale' | 'haute' | 'critique';
    urgent: boolean;
    client: Client;
    marchand?: Marchand;
    assigne_a?: number;
    assigneA?: User;
    date_ouverture: string;
    date_limite_reponse?: string;
    created_at: string;
    commande_principale?: CommandePrincipale;
    montant_conteste?: number;
}

interface DisputeMessage {
    id: number;
    message: string;
    auteur_type: 'client' | 'marchand' | 'admin';
    auteur_nom: string;
    type_message: string;
    pieces_jointes?: Array<{
        nom_original: string;
        nom_fichier: string;
        chemin: string;
        taille: number;
        type_mime: string;
    }>;
    interne: boolean;
    lu_par_admin: boolean;
    created_at: string;
    metadata?: any;
}

interface DisputeDetail {
    dispute: Dispute;
    messages: DisputeMessage[];
}

interface Admin {
    id: number;
    name: string;
    email: string;
}

export default function Disputes() {
    const [disputes, setDisputes] = useState<Dispute[]>([]);
    const [selectedDispute, setSelectedDispute] = useState<DisputeDetail | null>(null);
    const [admins, setAdmins] = useState<Admin[]>([]);
    const [loading, setLoading] = useState(true);

    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [priorityFilter, setPriorityFilter] = useState('');
    const [assignedFilter, setAssignedFilter] = useState('');
    const [newMessage, setNewMessage] = useState('');
    const [messageType, setMessageType] = useState('message');
    const [isInternal, setIsInternal] = useState(false);
    const [attachments, setAttachments] = useState<File[]>([]);
    const [sending, setSending] = useState(false);
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [newStatus, setNewStatus] = useState('');
    const [statusMessage, setStatusMessage] = useState('');
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { isOpen, imageData, openModal, closeModal } = useImageModal();

    // Hook WebSocket pour les notifications globales admin
    const { } = useReverbWebSocket({
        onMessage: (data: any) => {
            // Mettre à jour la liste des litiges si un nouveau message arrive
            if (data.dispute && selectedDispute?.dispute.id === parseInt(data.dispute.id)) {
                // Recharger les messages du litige sélectionné
                loadDispute(selectedDispute.dispute.id);
            }
            // Recharger la liste des litiges pour mettre à jour les compteurs
            loadDisputes();
        },
        onNotification: (data: any) => {
            console.log('Notification admin reçue:', data);
        },
    });

    // Charger les litiges
    const loadDisputes = async () => {
        try {
            const response = await axios.get('/api/disputes', {
                params: {
                    search: searchTerm,
                    status: statusFilter,
                    priority: priorityFilter,
                    assigned_to: assignedFilter
                }
            });
            setDisputes(response.data.disputes);
        } catch (error) {
            console.error('Erreur lors du chargement des litiges:', error);
        } finally {
            setLoading(false);
        }
    };

    // Charger un litige spécifique
    const loadDispute = async (id: number) => {

        try {
            const response = await axios.get(`/api/disputes/${id}`);
            setSelectedDispute(response.data);
            setSidebarOpen(false); // Fermer la sidebar sur mobile
        } catch (error) {
            console.error('Erreur lors du chargement du litige:', error);
        }
    };

    // Charger la liste des admins
    const loadAdmins = async () => {
        try {
            const response = await axios.get('/api/disputes/admins');
            setAdmins(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des admins:', error);
        }
    };

    // Envoyer un message
    const sendMessage = async () => {
        if (!newMessage.trim() || !selectedDispute || sending) return;

        setSending(true);
        try {
            const formData = new FormData();
            formData.append('message', newMessage);
            formData.append('type_message', messageType);
            formData.append('interne', isInternal ? '1' : '0');

            attachments.forEach((file, index) => {
                formData.append(`pieces_jointes[${index}]`, file);
            });

            const response = await axios.post(
                `/api/disputes/${selectedDispute.dispute.id}/messages`,
                formData,
                { headers: { 'Content-Type': 'multipart/form-data' } }
            );

            // Ajouter le nouveau message au litige
            setSelectedDispute(prev => prev ? {
                ...prev,
                messages: [...prev.messages, response.data.message]
            } : null);

            // Réinitialiser le formulaire
            setNewMessage('');
            setMessageType('message');
            setIsInternal(false);
            setAttachments([]);

            // Recharger les litiges pour mettre à jour les statuts
            loadDisputes();
        } catch (error) {
            console.error('Erreur lors de l\'envoi du message:', error);
        } finally {
            setSending(false);
        }
    };

    // Envoyer un message via WebSocket (nouvelle fonction)
    const handleRealtimeSendMessage = async (message: string, type: string, files?: File[]) => {
        if (!selectedDispute) return;

        const formData = new FormData();
        formData.append('message', message);
        formData.append('type_message', type);

        if (files) {
            files.forEach((file, index) => {
                formData.append(`pieces_jointes[${index}]`, file);
            });
        }

        const response = await fetch(
            `/api/disputes/${selectedDispute.dispute.id}/messages`,
            {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: formData,
            }
        );

        if (!response.ok) {
            throw new Error('Erreur lors de l\'envoi du message');
        }

        // Le message sera automatiquement ajouté via WebSocket
        // Recharger les litiges pour mettre à jour les compteurs
        loadDisputes();
    };

    // Changer le statut d'un litige
    const updateStatus = async () => {
        if (!selectedDispute || !newStatus) return;

        try {
            await axios.patch(`/api/disputes/${selectedDispute.dispute.id}/status`, {
                statut: newStatus,
                message: statusMessage
            });

            setShowStatusModal(false);
            setNewStatus('');
            setStatusMessage('');
            loadDisputes();
            loadDispute(selectedDispute.dispute.id);
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
        }
    };

    // Assigner un litige
    const assignDispute = async (adminId: number) => {
        if (!selectedDispute) return;

        try {
            await axios.patch(`/api/disputes/${selectedDispute.dispute.id}/assign`, {
                admin_id: adminId
            });

            loadDisputes();
            loadDispute(selectedDispute.dispute.id);
        } catch (error) {
            console.error('Erreur lors de l\'assignation:', error);
        }
    };



    // Auto-scroll vers le bas
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        loadDisputes();
        loadAdmins();
    }, [searchTerm, statusFilter, priorityFilter, assignedFilter]);

    useEffect(() => {
        if (selectedDispute) {
            scrollToBottom();
        }
    }, [selectedDispute?.messages]);

    // Formatage des dates
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 jours
            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ouvert': return 'bg-yellow-100 text-yellow-800';
            case 'en_cours': return 'bg-blue-100 text-blue-800';
            case 'attente_client': return 'bg-purple-100 text-purple-800';
            case 'attente_marchand': return 'bg-orange-100 text-orange-800';
            case 'escalade': return 'bg-red-100 text-red-800';
            case 'resolu': return 'bg-green-100 text-green-800';
            case 'ferme': return 'bg-gray-100 text-gray-800';
            case 'annule': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'basse': return 'bg-gray-100 text-gray-800';
            case 'normale': return 'bg-blue-100 text-blue-800';
            case 'haute': return 'bg-orange-100 text-orange-800';
            case 'critique': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'ouvert': return 'Ouvert';
            case 'en_cours': return 'En cours';
            case 'attente_client': return 'Attente client';
            case 'attente_marchand': return 'Attente marchand';
            case 'escalade': return 'Escaladé';
            case 'resolu': return 'Résolu';
            case 'ferme': return 'Fermé';
            case 'annule': return 'Annulé';
            default: return status;
        }
    };

    const getPriorityLabel = (priority: string) => {
        switch (priority) {
            case 'basse': return 'Basse';
            case 'normale': return 'Normale';
            case 'haute': return 'Haute';
            case 'critique': return 'Critique';
            default: return priority;
        }
    };

    return (
        <>
            <Head title="Litiges - Dashboard Admin" />

            <div className="h-screen flex bg-gray-50 dark:bg-gray-900">
                {/* Overlay mobile */}
                {sidebarOpen && (
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                        onClick={() => setSidebarOpen(false)}
                    />
                )}

                {/* Sidebar des litiges */}
                <div className={`
                    ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                    lg:translate-x-0 fixed lg:relative z-50 lg:z-auto
                    w-80 lg:w-1/3 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
                    flex flex-col transition-transform duration-300 ease-in-out
                `}>
                    {/* Header */}
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <h1 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                                <ExclamationTriangleIcon className="w-6 h-6 mr-2 text-red-600 dark:text-red-400" />
                                Litiges
                            </h1>
                            <div className="flex items-center space-x-2">
                                <ThemeSelector className="hidden lg:block" />
                                <button
                                    onClick={() => setSidebarOpen(false)}
                                    className="lg:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                                >
                                    <XMarkIcon className="w-5 h-5" />
                                </button>
                            </div>
                        </div>

                        {/* Recherche et filtres */}
                        <div className="mt-4 space-y-2">
                            <div className="relative">
                                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                                <input
                                    type="text"
                                    placeholder="Rechercher..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                             bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                             focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                             placeholder-gray-500 dark:placeholder-gray-400"
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-2">
                                <select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                             bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                             focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                >
                                    <option value="">Tous les statuts</option>
                                    <option value="ouvert">Ouvert</option>
                                    <option value="en_cours">En cours</option>
                                    <option value="attente_client">Attente client</option>
                                    <option value="attente_marchand">Attente marchand</option>
                                    <option value="escalade">Escaladé</option>
                                    <option value="resolu">Résolu</option>
                                </select>

                                <select
                                    value={priorityFilter}
                                    onChange={(e) => setPriorityFilter(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                             bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                             focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                >
                                    <option value="">Toutes priorités</option>
                                    <option value="basse">Basse</option>
                                    <option value="normale">Normale</option>
                                    <option value="haute">Haute</option>
                                    <option value="critique">Critique</option>
                                </select>
                            </div>

                            <select
                                value={assignedFilter}
                                onChange={(e) => setAssignedFilter(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                            >
                                <option value="">Toutes assignations</option>
                                <option value="me">Mes litiges</option>
                                <option value="unassigned">Non assignés</option>
                            </select>
                        </div>
                    </div>

                    {/* Liste des litiges */}
                    <div className="flex-1 overflow-y-auto">
                        {loading ? (
                            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                                <ArrowPathIcon className="w-6 h-6 animate-spin mx-auto mb-2" />
                                Chargement...
                            </div>
                        ) : disputes.length === 0 ? (
                            <div className="p-4 text-center text-gray-500 dark:text-gray-400">Aucun litige trouvé</div>
                        ) : (
                            disputes.map((dispute) => (
                                <div
                                    key={dispute.id}
                                    onClick={() => loadDispute(dispute.id)}
                                    className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer
                                              hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                                        selectedDispute?.dispute.id === dispute.id
                                            ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700'
                                            : ''
                                    }`}
                                >
                                    <div className="flex items-start space-x-3">
                                        {/* Avatar */}
                                        <div className="flex-shrink-0">
                                            {dispute.client.user.avatar ? (
                                                <img
                                                    src={dispute.client.user.avatar}
                                                    alt={dispute.client.user.name}
                                                    className="w-10 h-10 rounded-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                    <UserIcon className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Contenu */}
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-2">
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                        #{dispute.numero_litige}
                                                    </p>
                                                    {dispute.urgent && (
                                                        <ExclamationTriangleIcon className="w-4 h-4 text-red-500 dark:text-red-400" />
                                                    )}
                                                </div>
                                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                                    {formatDate(dispute.created_at)}
                                                </span>
                                            </div>

                                            <p className="text-sm text-gray-900 dark:text-white font-medium mt-1">
                                                {dispute.client.user.name}
                                            </p>

                                            <p className="text-sm text-gray-600 dark:text-gray-300 truncate mt-1">
                                                {dispute.description}
                                            </p>

                                            <div className="flex items-center justify-between mt-2">
                                                <div className="flex items-center space-x-2">
                                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(dispute.statut)}`}>
                                                        {getStatusLabel(dispute.statut)}
                                                    </span>

                                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(dispute.priorite)}`}>
                                                        {getPriorityLabel(dispute.priorite)}
                                                    </span>
                                                </div>

                                                {dispute.assigneA && (
                                                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                                        <UserGroupIcon className="w-3 h-3 mr-1" />
                                                        {dispute.assigneA.name}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                {/* Zone de chat */}
                <div className="flex-1 flex flex-col relative">
                    {/* Bouton menu mobile */}
                    <button
                        onClick={() => setSidebarOpen(true)}
                        className="lg:hidden fixed top-4 left-4 z-30 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
                    >
                        <Bars3Icon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    </button>

                    {selectedDispute ? (
                        <>
                            {/* Header du litige */}
                            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        {selectedDispute.dispute.client.user.avatar ? (
                                            <img
                                                src={selectedDispute.dispute.client.user.avatar}
                                                alt={selectedDispute.dispute.client.user.name}
                                                className="w-10 h-10 rounded-full"
                                            />
                                        ) : (
                                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                <UserIcon className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                                            </div>
                                        )}

                                        <div>
                                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                                Litige #{selectedDispute.dispute.numero_litige}
                                                {selectedDispute.dispute.urgent && (
                                                    <ExclamationTriangleIcon className="w-5 h-5 ml-2 text-red-500 dark:text-red-400" />
                                                )}
                                                <div className="ml-3">
                                                    <ConnectionIndicator size="sm" />
                                                </div>
                                            </h2>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                                {selectedDispute.dispute.client.user.name}
                                                {selectedDispute.dispute.marchand && (
                                                    <span> • {selectedDispute.dispute.marchand.nom_entreprise}</span>
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedDispute.dispute.statut)}`}>
                                            {getStatusLabel(selectedDispute.dispute.statut)}
                                        </span>

                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedDispute.dispute.priorite)}`}>
                                            {getPriorityLabel(selectedDispute.dispute.priorite)}
                                        </span>

                                        <button
                                            onClick={() => setShowStatusModal(true)}
                                            className="px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300
                                                     border border-blue-300 dark:border-blue-600 rounded-lg
                                                     hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors cursor-pointer"
                                        >
                                            Changer statut
                                        </button>

                                        {/* Assignation */}
                                        <select
                                            value={selectedDispute.dispute.assigne_a || ''}
                                            onChange={(e) => e.target.value && assignDispute(parseInt(e.target.value))}
                                            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg
                                                     bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                     focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                                        >
                                            <option value="">Assigner à...</option>
                                            {admins.map((admin) => (
                                                <option key={admin.id} value={admin.id}>
                                                    {admin.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                {/* Informations du litige */}
                                <div className="mt-4 space-y-4">
                                    {/* Informations générales */}
                                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                                            <ExclamationTriangleIcon className="w-4 h-4 mr-2 text-orange-500 dark:text-orange-400" />
                                            Informations du litige
                                        </h3>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <span className="font-medium text-gray-500 dark:text-gray-400">Type:</span>
                                                <p className="text-gray-900 dark:text-white capitalize">{selectedDispute.dispute.type_litige.replace('_', ' ')}</p>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-500 dark:text-gray-400">Ouvert le:</span>
                                                <p className="text-gray-900 dark:text-white">{new Date(selectedDispute.dispute.date_ouverture).toLocaleDateString('fr-FR')}</p>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-500 dark:text-gray-400">Assigné à:</span>
                                                <p className="text-gray-900 dark:text-white">{selectedDispute.dispute.assigneA?.name || 'Non assigné'}</p>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-500 dark:text-gray-400">Limite réponse:</span>
                                                <p className="text-gray-900 dark:text-white">
                                                    {selectedDispute.dispute.date_limite_reponse
                                                        ? new Date(selectedDispute.dispute.date_limite_reponse).toLocaleDateString('fr-FR')
                                                        : 'Aucune'
                                                    }
                                                </p>
                                            </div>
                                        </div>

                                        {selectedDispute.dispute.montant_conteste && (
                                            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                                                <span className="font-medium text-gray-500 dark:text-gray-400">Montant contesté:</span>
                                                <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                                                    {selectedDispute.dispute.montant_conteste.toLocaleString('fr-FR')} FCFA
                                                </p>
                                            </div>
                                        )}

                                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                                            <span className="font-medium text-gray-500 dark:text-gray-400">Description:</span>
                                            <p className="text-gray-900 dark:text-white mt-1">{selectedDispute.dispute.description}</p>
                                        </div>
                                    </div>

                                    {/* Informations de la commande */}
                                    {selectedDispute.dispute.commande_principale && (
                                        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                                            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                                                <ShoppingCartIcon className="w-4 h-4 mr-2 text-blue-500 dark:text-blue-400" />
                                                Commande concernée
                                            </h3>
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                                <div>
                                                    <span className="font-medium text-gray-500 dark:text-gray-400">N° Commande:</span>
                                                    <p className="text-gray-900 dark:text-white font-mono">#{selectedDispute.dispute.commande_principale.numero_commande}</p>
                                                </div>
                                                <div>
                                                    <span className="font-medium text-gray-500 dark:text-gray-400">Montant total:</span>
                                                    <p className="text-gray-900 dark:text-white font-semibold">
                                                        {selectedDispute.dispute.commande_principale.montant_total_ttc.toLocaleString('fr-FR')} FCFA
                                                    </p>
                                                </div>
                                                <div>
                                                    <span className="font-medium text-gray-500 dark:text-gray-400">Statut commande:</span>
                                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                                        selectedDispute.dispute.commande_principale.statut_global === 'livree'
                                                            ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400'
                                                            : selectedDispute.dispute.commande_principale.statut_global === 'en_cours'
                                                            ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400'
                                                            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                                                    }`}>
                                                        {selectedDispute.dispute.commande_principale.statut_global.replace('_', ' ')}
                                                    </span>
                                                </div>
                                                <div>
                                                    <span className="font-medium text-gray-500 dark:text-gray-400">Date commande:</span>
                                                    <p className="text-gray-900 dark:text-white">
                                                        {new Date(selectedDispute.dispute.commande_principale.date_commande).toLocaleDateString('fr-FR')}
                                                    </p>
                                                </div>
                                            </div>

                                            {/* Produits de la commande */}
                                            {selectedDispute.dispute.commande_principale.produits && selectedDispute.dispute.commande_principale.produits.length > 0 && (
                                                <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                                                    <span className="font-medium text-gray-500 dark:text-gray-400 block mb-2">Produits commandés:</span>
                                                    <div className="space-y-2">
                                                        {selectedDispute.dispute.commande_principale.produits.map((produit, index) => (
                                                            <div key={index} className="flex items-center space-x-3 p-2 bg-white dark:bg-gray-800 rounded-lg">
                                                                {produit.image_principale ? (
                                                                    <img
                                                                        src={produit.image_principale}
                                                                        alt={produit.nom}
                                                                        className="w-12 h-12 object-cover rounded-lg"
                                                                    />
                                                                ) : (
                                                                    <div className="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                                                                        <span className="text-2xl">📦</span>
                                                                    </div>
                                                                )}
                                                                <div className="flex-1 min-w-0">
                                                                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{produit.nom}</p>
                                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                                        Quantité: {produit.quantite} • Prix: {produit.prix_unitaire.toLocaleString('fr-FR')} FCFA
                                                                    </p>
                                                                </div>
                                                                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                                                                    {(produit.prix_unitaire * produit.quantite).toLocaleString('fr-FR')} FCFA
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Chat WebSocket amélioré */}
                            <div className="flex-1 flex flex-col">
                                <EnhancedDisputeChat
                                    disputeId={selectedDispute.dispute.id.toString()}
                                    messages={selectedDispute.messages}
                                    currentUserId="admin" // TODO: Récupérer l'ID de l'admin connecté
                                    currentUserType="admin"
                                    onSendMessage={handleRealtimeSendMessage}
                                    onImageClick={(imageData) => openModal(imageData.url, imageData.name)}
                                    className="h-full"
                                />
                            </div>
                        </>
                    ) : (
                        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900 pl-16 lg:pl-0">
                            <div className="text-center">
                                <ExclamationTriangleIcon className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Sélectionnez un litige</h3>
                                <p className="text-gray-500 dark:text-gray-400">Choisissez un litige dans la liste pour commencer à le traiter</p>
                                <button
                                    onClick={() => setSidebarOpen(true)}
                                    className="mt-4 lg:hidden px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Voir les litiges
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                {/* Modal de changement de statut */}
                {showStatusModal && selectedDispute && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Changer le statut du litige</h3>

                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Nouveau statut
                                    </label>
                                    <select
                                        value={newStatus}
                                        onChange={(e) => setNewStatus(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                                 bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Sélectionner un statut</option>
                                        <option value="ouvert">Ouvert</option>
                                        <option value="en_cours">En cours</option>
                                        <option value="attente_client">Attente client</option>
                                        <option value="attente_marchand">Attente marchand</option>
                                        <option value="escalade">Escaladé</option>
                                        <option value="resolu">Résolu</option>
                                        <option value="ferme">Fermé</option>
                                        <option value="annule">Annulé</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Message explicatif (optionnel)
                                    </label>
                                    <textarea
                                        value={statusMessage}
                                        onChange={(e) => setStatusMessage(e.target.value)}
                                        rows={3}
                                        placeholder="Expliquez le changement de statut..."
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                                 bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                 focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                                 placeholder-gray-500 dark:placeholder-gray-400"
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end space-x-3 mt-6">
                                <button
                                    onClick={() => setShowStatusModal(false)}
                                    className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600
                                             rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                >
                                    Annuler
                                </button>
                                <button
                                    onClick={updateStatus}
                                    disabled={!newStatus}
                                    className="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg
                                             hover:bg-blue-700 dark:hover:bg-blue-600
                                             disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    Mettre à jour
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Modal d'image */}
            {imageData && (
                <ImageModal
                    isOpen={isOpen}
                    onClose={closeModal}
                    imageUrl={imageData.url}
                    imageName={imageData.name}
                    imageSize={imageData.size}
                />
            )}

            {/* Notifications WebSocket */}
            <NotificationToast
                position="top-right"
                maxNotifications={3}
                autoHideDelay={5000}
            />
        </>
    );
}
