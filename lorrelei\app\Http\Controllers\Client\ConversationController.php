<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\ClientMarchandConversation;
use App\Models\ConversationMessage;
use App\Models\CommandePrincipale;
use App\Models\Marchand;
use App\Models\Produit;
use App\Services\ClientMarchandMessagingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Inertia\Inertia;

class ConversationController extends Controller
{
    protected ClientMarchandMessagingService $messagingService;

    public function __construct(ClientMarchandMessagingService $messagingService)
    {
        $this->messagingService = $messagingService;
    }

    /**
     * Affiche la liste des conversations du client
     */
    public function index(Request $request)
    {
        $client = Auth::user()->client;

        // Récupérer les conversations avec pagination
        $result = $this->messagingService->obtenirConversationsClient(
            $client->id,
            $request->only(['statut', 'type', 'marchand_id', 'per_page'])
        );

        if (!$result['success']) {
            return back()->withErrors(['error' => 'Erreur lors du chargement des conversations']);
        }

        // Récupérer les statistiques
        $statsResult = $this->messagingService->obtenirStatistiquesClient($client->id);
        $stats = $statsResult['success'] ? $statsResult['stats'] : [
            'total_conversations' => 0,
            'conversations_actives' => 0,
            'messages_non_lus' => 0,
            'conversations_recentes' => 0,
        ];

        return Inertia::render('Client/Conversations/Index', [
            'conversations' => [
                'data' => $result['conversations']->items(),
                'meta' => [
                    'current_page' => $result['conversations']->currentPage(),
                    'last_page' => $result['conversations']->lastPage(),
                    'per_page' => $result['conversations']->perPage(),
                    'total' => $result['conversations']->total(),
                    'from' => $result['conversations']->firstItem(),
                    'to' => $result['conversations']->lastItem(),
                ],
                'links' => $result['conversations']->links()->elements
            ],
            'stats' => $stats,
            'filters' => $request->only(['statut', 'type', 'marchand_id']),
            'typesConversation' => $this->getTypesConversation(),
            'statutsConversation' => $this->getStatutsConversation()
        ]);
    }

    /**
     * Affiche le formulaire de création d'une conversation
     */
    public function create(Request $request)
    {
        $client = Auth::user()->client;

        // Récupérer les commandes du client pour le contexte
        $commandes = CommandePrincipale::where('client_id', $client->id)
            ->where('statut_global', '!=', 'annulee')
            ->with(['sousCommandes.marchand'])
            ->orderBy('date_commande', 'desc')
            ->limit(20)
            ->get();

        // Si un marchand est spécifié dans l'URL
        $marchandSelectionne = null;
        if ($request->has('marchand_id')) {
            $marchand = Marchand::find($request->marchand_id);
            if ($marchand) {
                $marchandSelectionne = [
                    'id' => $marchand->id,
                    'nom_entreprise' => $marchand->nomEntreprise
                ];
            }
        }

        // Si une commande est spécifiée dans l'URL
        $commandeSelectionnee = null;
        if ($request->has('commande_id')) {
            $commandeSelectionnee = CommandePrincipale::where('id', $request->commande_id)
                ->where('client_id', $client->id)
                ->with(['sousCommandes.marchand'])
                ->first();
        }

        // Si un produit est spécifié dans l'URL
        $produitSelectionne = null;
        if ($request->has('produit_id')) {
            $produit = Produit::with('marchand')->find($request->produit_id);
            if ($produit) {
                // Transformer le produit pour le frontend
                $produitSelectionne = [
                    'id' => $produit->id,
                    'nom' => $produit->getTranslation('nom', app()->getLocale()) ?: $produit->nom,
                    'image_principale' => $produit->image_urls[0] ?? null,
                    'marchand' => [
                        'id' => $produit->marchand->id,
                        'nom_entreprise' => $produit->marchand->nomEntreprise
                    ]
                ];
            }
        }

        // Récupérer tous les marchands actifs pour la sélection
        // Prioriser les marchands avec qui le client a déjà commandé
        $marchandsDesCommandes = collect();

        foreach ($commandes as $commande) {
            if ($commande->sousCommandes) {
                foreach ($commande->sousCommandes as $sousCommande) {
                    if ($sousCommande->marchand && !$marchandsDesCommandes->contains('id', $sousCommande->marchand->id)) {
                        $marchandsDesCommandes->push([
                            'id' => $sousCommande->marchand->id,
                            'nom_entreprise' => $sousCommande->marchand->nomEntreprise
                        ]);
                    }
                }
            }
        }

        // Récupérer tous les autres marchands actifs (seulement ceux validés)
        $autresMarchands = Marchand::where('statut_validation', 'valide')
            ->whereNotIn('id', $marchandsDesCommandes->pluck('id'))
            ->select('id', 'nomEntreprise')
            ->orderBy('nomEntreprise')
            ->get()
            ->map(function($marchand) {
                return [
                    'id' => $marchand->id,
                    'nom_entreprise' => $marchand->nomEntreprise
                ];
            });

        // Combiner les deux listes : d'abord ceux des commandes, puis les autres
        $tousLesMarchands = $marchandsDesCommandes->concat($autresMarchands);

        // Debug : Log des données
        Log::info('Debug Conversations Create', [
            'commandes_count' => $commandes->count(),
            'commandes_data' => $commandes->toArray(),
            'marchands_des_commandes_count' => $marchandsDesCommandes->count(),
            'autres_marchands_count' => $autresMarchands->count(),
            'tous_les_marchands_count' => $tousLesMarchands->count(),
        ]);

        return Inertia::render('Client/Conversations/Create', [
            'commandes' => $commandes,
            'marchandSelectionne' => $marchandSelectionne,
            'commandeSelectionnee' => $commandeSelectionnee,
            'produitSelectionne' => $produitSelectionne,
            'marchandsDisponibles' => $tousLesMarchands,
            'typesConversation' => $this->getTypesConversation(),
            'maxFileSize' => 5120, // 5MB en KB
        ]);
    }

    /**
     * Crée une nouvelle conversation
     */
    public function store(Request $request)
    {
        $client = Auth::user()->client;

        $request->validate([
            'marchand_id' => 'required|exists:marchands,id',
            'sujet' => 'required|string|max:255',
            'type_conversation' => 'required|in:question_produit,probleme_commande,demande_info,reclamation,autre',
            'message_initial' => 'required|string|max:2000',
            'commande_principale_id' => 'nullable|exists:commandes_principales,id',
            'sous_commande_id' => 'nullable|exists:sous_commandes_vendeur,id',
            'produit_id' => 'nullable|exists:produits,id',
            'pieces_jointes.*' => 'file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120',
        ]);

        try {
            // Traiter les fichiers uploadés
            $piecesJointes = [];
            if ($request->hasFile('pieces_jointes')) {
                foreach ($request->file('pieces_jointes') as $file) {
                    $path = $file->store('conversations', 'public');
                    $piecesJointes[] = [
                        'nom_original' => $file->getClientOriginalName(),
                        'chemin' => $path,
                        'taille' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Créer la conversation
            $result = $this->messagingService->creerConversation([
                'client_id' => $client->id,
                'marchand_id' => $request->marchand_id,
                'commande_principale_id' => $request->commande_principale_id ?: null,
                'sous_commande_id' => $request->sous_commande_id ?: null,
                'produit_id' => $request->produit_id ?: null,
                'sujet' => $request->sujet,
                'type_conversation' => $request->type_conversation,
                'message_initial' => $request->message_initial,
                'pieces_jointes' => $piecesJointes,
                'source' => 'web_client',
            ]);

            if (!$result['success']) {
                return back()
                    ->withErrors(['error' => 'Erreur lors de la création de la conversation'])
                    ->withInput();
            }

            Log::info('Conversation créée par client', [
                'client_id' => $client->id,
                'conversation_id' => $result['conversation']->id,
                'marchand_id' => $request->marchand_id
            ]);

            return redirect()
                ->route('client.conversations.show', $result['conversation']->id)
                ->with('success', $result['nouvelle'] ? 'Conversation créée avec succès' : 'Conversation existante trouvée');

        } catch (\Exception $e) {
            Log::error('Erreur création conversation client', [
                'client_id' => $client->id,
                'error' => $e->getMessage(),
                'request_data' => $request->except(['pieces_jointes'])
            ]);

            return back()
                ->withErrors(['error' => 'Une erreur est survenue lors de la création de la conversation'])
                ->withInput();
        }
    }

    /**
     * Affiche les détails d'une conversation
     */
    public function show(string $id)
    {
        $client = Auth::user()->client;

        $conversation = ClientMarchandConversation::where('id', $id)
            ->where('client_id', $client->id)
            ->with([
                'marchand',
                'commandePrincipale',
                'sousCommande.marchand',
                'produit',
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                }
            ])
            ->firstOrFail();

        // Marquer les messages comme lus par le client
        $this->messagingService->marquerCommeLu($conversation->id, 'client');

        return Inertia::render('Client/Conversations/Show', [
            'conversation' => $conversation,
            'canAddMessage' => $conversation->statut === 'active',
            'canClose' => $conversation->statut === 'active',
            'canReopen' => $conversation->peutEtreRouverte(),
        ]);
    }

    /**
     * Ajoute un message à une conversation
     */
    public function addMessage(Request $request, string $id)
    {
        $client = Auth::user()->client;

        $request->validate([
            'message' => 'required|string|max:2000',
            'pieces_jointes.*' => 'file|mimes:jpg,jpeg,png,pdf|max:5120',
        ]);

        try {
            // Vérifier que la conversation appartient au client
            $conversation = ClientMarchandConversation::where('id', $id)
                ->where('client_id', $client->id)
                ->firstOrFail();

            // Traiter les fichiers uploadés
            $piecesJointes = [];
            if ($request->hasFile('pieces_jointes')) {
                foreach ($request->file('pieces_jointes') as $file) {
                    $path = $file->store('conversations', 'public');
                    $piecesJointes[] = [
                        'nom_original' => $file->getClientOriginalName(),
                        'chemin' => $path,
                        'taille' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Ajouter le message
            $result = $this->messagingService->ajouterMessage($conversation->id, [
                'auteur_type' => 'client',
                'auteur_client_id' => $client->id,
                'auteur_nom' => $client->nom . ' ' . $client->prenom,
                'message' => $request->message,
                'type_message' => 'message',
                'pieces_jointes' => $piecesJointes,
            ]);

            if (!$result['success']) {
                return back()->withErrors(['error' => 'Erreur lors de l\'ajout du message']);
            }

            return back()->with('success', 'Message envoyé avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur ajout message conversation', [
                'client_id' => $client->id,
                'conversation_id' => $id,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Une erreur est survenue lors de l\'envoi du message']);
        }
    }

    /**
     * Ferme une conversation
     */
    public function close(string $id)
    {
        $client = Auth::user()->client;

        try {
            // Vérifier que la conversation appartient au client
            $conversation = ClientMarchandConversation::where('id', $id)
                ->where('client_id', $client->id)
                ->firstOrFail();

            $result = $this->messagingService->fermerConversation($conversation->id, 'client', $client->id);

            if (!$result['success']) {
                return back()->withErrors(['error' => 'Erreur lors de la fermeture de la conversation']);
            }

            return back()->with('success', 'Conversation fermée avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur fermeture conversation', [
                'client_id' => $client->id,
                'conversation_id' => $id,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Une erreur est survenue lors de la fermeture']);
        }
    }

    /**
     * Rouvre une conversation
     */
    public function reopen(string $id)
    {
        $client = Auth::user()->client;

        try {
            // Vérifier que la conversation appartient au client
            $conversation = ClientMarchandConversation::where('id', $id)
                ->where('client_id', $client->id)
                ->firstOrFail();

            if (!$conversation->peutEtreRouverte()) {
                return back()->withErrors(['error' => 'Cette conversation ne peut plus être rouverte']);
            }

            $conversation->rouvrir();

            return back()->with('success', 'Conversation rouverte avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur réouverture conversation', [
                'client_id' => $client->id,
                'conversation_id' => $id,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Une erreur est survenue lors de la réouverture']);
        }
    }

    /**
     * Obtient les types de conversation disponibles
     */
    private function getTypesConversation(): array
    {
        return [
            'question_produit' => 'Question sur un produit',
            'probleme_commande' => 'Problème avec une commande',
            'demande_info' => 'Demande d\'information',
            'reclamation' => 'Réclamation',
            'autre' => 'Autre sujet'
        ];
    }

    /**
     * Obtient les statuts de conversation disponibles
     */
    private function getStatutsConversation(): array
    {
        return [
            'active' => 'Active',
            'fermee' => 'Fermée',
            'archivee' => 'Archivée',
            'bloquee' => 'Bloquée'
        ];
    }

    /**
     * Télécharge une pièce jointe d'un message de conversation
     */
    public function downloadAttachment(string $messageId, string $attachmentIndex): HttpResponse
    {
        $client = Auth::user()->client;

        if (!$client) {
            abort(403, 'Accès non autorisé');
        }

        try {
            // Récupérer le message et vérifier qu'il appartient à une conversation du client
            $message = ConversationMessage::where('id', $messageId)
                ->whereHas('conversation', function($query) use ($client) {
                    $query->where('client_id', $client->id);
                })
                ->firstOrFail();

            // Vérifier que la pièce jointe existe
            if (!$message->pieces_jointes || !isset($message->pieces_jointes[$attachmentIndex])) {
                abort(404, 'Pièce jointe non trouvée');
            }

            $attachment = $message->pieces_jointes[$attachmentIndex];

            // Vérifier que le fichier existe sur le disque
            $filePath = $attachment['chemin'];
            if (!Storage::disk('public')->exists($filePath)) {
                abort(404, 'Fichier non trouvé sur le serveur');
            }

            // Log du téléchargement
            Log::info('Téléchargement pièce jointe conversation', [
                'client_id' => $client->id,
                'message_id' => $messageId,
                'conversation_id' => $message->conversation_id,
                'attachment' => $attachment['nom_original'],
                'file_path' => $filePath
            ]);

            // Retourner le fichier
            return response()->download(
                Storage::disk('public')->path($filePath),
                $attachment['nom_original'],
                [
                    'Content-Type' => $attachment['type'] ?? 'application/octet-stream'
                ]
            );

        } catch (\Exception $e) {
            Log::error('Erreur téléchargement pièce jointe conversation', [
                'client_id' => $client->id,
                'message_id' => $messageId,
                'attachment_index' => $attachmentIndex,
                'error' => $e->getMessage()
            ]);

            abort(500, 'Erreur lors du téléchargement');
        }
    }
}
