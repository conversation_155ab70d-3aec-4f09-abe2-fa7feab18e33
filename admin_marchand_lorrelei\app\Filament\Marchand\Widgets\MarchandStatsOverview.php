<?php

namespace App\Filament\Marchand\Widgets;

use App\Services\DashboardStatsService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MarchandStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $marchandId = auth()->user()->marchands->first()->id ?? null;

        if (!$marchandId) {
            return [];
        }

        $dashboardService = new DashboardStatsService();
        $stats = $dashboardService->getStatistiquesMarchand($marchandId);

        return [
            Stat::make('Commandes ce mois', $stats['commandesMois'])
                ->description('Nouveau + ancien système')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->chartColor('primary'),

            Stat::make('Commandes en attente', $stats['commandesEnAttente'])
                ->description('En attente de traitement')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Commandes livrées', $stats['commandesLivrees'])
                ->description('Commandes complétées')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Revenus ce mois', number_format($stats['revenusMois'], 0, ',', ' ') . ' FCFA')
                ->description($stats['evolutionRevenus'] >= 0 ? '+' . $stats['evolutionRevenus'] . '% vs mois dernier' : $stats['evolutionRevenus'] . '% vs mois dernier')
                ->descriptionIcon($stats['evolutionRevenus'] >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($stats['evolutionRevenus'] >= 0 ? 'success' : 'danger'),

            Stat::make('Produits actifs', $stats['produitsActifs'])
                ->description('Produits en vente')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('primary'),

            Stat::make('Produits en rupture', $stats['produitsEnRupture'])
                ->description('À réapprovisionner')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color($stats['produitsEnRupture'] > 0 ? 'danger' : 'success'),

            Stat::make('Versements en attente', number_format($stats['montantVersementsEnAttente'], 0, ',', ' ') . ' FCFA')
                ->description($stats['versementsEnAttente'] . ' versement(s)')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),

            Stat::make('Commissions collectées', number_format($stats['montantCommissions'], 0, ',', ' ') . ' FCFA')
                ->description($stats['commissionsCollectees'] . ' commission(s)')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('info'),
        ];
    }
}
