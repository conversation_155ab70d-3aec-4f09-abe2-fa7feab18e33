import React, { useState, useEffect } from 'react';
import { BellIcon, ChatBubbleLeftRightIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import axios from 'axios';

interface NotificationData {
    conversations_non_lues: number;
    disputes_non_lues: number;
    total_non_lus: number;
}

interface NotificationBadgeProps {
    className?: string;
    showDetails?: boolean;
}

export default function NotificationBadge({ className = '', showDetails = false }: NotificationBadgeProps) {
    const [notifications, setNotifications] = useState<NotificationData>({
        conversations_non_lues: 0,
        disputes_non_lues: 0,
        total_non_lus: 0
    });
    const [loading, setLoading] = useState(false);

    const fetchNotifications = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/notifications/unread-count');
            setNotifications(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des notifications:', error);
        } finally {
            setLoading(false);
        }
    };

    const markAllAsRead = async () => {
        try {
            await axios.post('/api/notifications/mark-all-read');
            setNotifications({
                conversations_non_lues: 0,
                disputes_non_lues: 0,
                total_non_lus: 0
            });
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    };

    useEffect(() => {
        fetchNotifications();
        
        // Actualiser toutes les 30 secondes
        const interval = setInterval(fetchNotifications, 30000);
        
        return () => clearInterval(interval);
    }, []);

    if (loading && notifications.total_non_lus === 0) {
        return (
            <div className={`relative ${className}`}>
                <BellIcon className="w-6 h-6 text-gray-400 animate-pulse" />
            </div>
        );
    }

    if (showDetails) {
        return (
            <div className={`space-y-2 ${className}`}>
                {/* Messages avec marchands */}
                {notifications.conversations_non_lues > 0 && (
                    <div className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div className="flex items-center space-x-2">
                            <ChatBubbleLeftRightIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            <span className="text-sm text-blue-800 dark:text-blue-300">
                                Messages marchands
                            </span>
                        </div>
                        <span className="bg-blue-600 dark:bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {notifications.conversations_non_lues}
                        </span>
                    </div>
                )}

                {/* Messages de litiges */}
                {notifications.disputes_non_lues > 0 && (
                    <div className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div className="flex items-center space-x-2">
                            <ExclamationTriangleIcon className="w-5 h-5 text-red-600 dark:text-red-400" />
                            <span className="text-sm text-red-800 dark:text-red-300">
                                Réponses litiges
                            </span>
                        </div>
                        <span className="bg-red-600 dark:bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {notifications.disputes_non_lues}
                        </span>
                    </div>
                )}

                {/* Bouton marquer tout comme lu */}
                {notifications.total_non_lus > 0 && (
                    <button
                        onClick={markAllAsRead}
                        className="w-full text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 
                                 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                    >
                        Marquer tout comme lu
                    </button>
                )}

                {notifications.total_non_lus === 0 && (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                        <BellIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Aucune notification</p>
                    </div>
                )}
            </div>
        );
    }

    // Affichage simple avec badge
    return (
        <div className={`relative ${className}`}>
            <BellIcon className="w-6 h-6 text-gray-600 dark:text-gray-300" />
            {notifications.total_non_lus > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold 
                               rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                    {notifications.total_non_lus > 99 ? '99+' : notifications.total_non_lus}
                </span>
            )}
        </div>
    );
}

// Hook pour utiliser les notifications dans d'autres composants
export function useNotifications() {
    const [notifications, setNotifications] = useState<NotificationData>({
        conversations_non_lues: 0,
        disputes_non_lues: 0,
        total_non_lus: 0
    });

    const fetchNotifications = async () => {
        try {
            const response = await axios.get('/api/notifications/unread-count');
            setNotifications(response.data);
            return response.data;
        } catch (error: any) {
            // Si erreur 401 (non authentifié), ne pas logger comme erreur
            if (error.response?.status === 401) {
                console.log('Utilisateur non authentifié, pas de notifications à charger');
            } else {
                console.error('Erreur lors du chargement des notifications:', error);
            }
            // En cas d'erreur, garder les valeurs par défaut
            setNotifications({
                conversations_non_lues: 0,
                disputes_non_lues: 0,
                total_non_lus: 0
            });
            return notifications;
        }
    };

    const markAllAsRead = async () => {
        try {
            await axios.post('/api/notifications/mark-all-read');
            setNotifications({
                conversations_non_lues: 0,
                disputes_non_lues: 0,
                total_non_lus: 0
            });
            return true;
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
            return false;
        }
    };

    useEffect(() => {
        // Seulement charger les notifications si on est dans un contexte authentifié
        const timer = setTimeout(() => {
            fetchNotifications();
        }, 100); // Petit délai pour laisser le temps à l'auth de se charger

        return () => clearTimeout(timer);
    }, []);

    return {
        notifications,
        fetchNotifications,
        markAllAsRead
    };
}
