import { useState, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { useReverbWebSocket } from '@/hooks/useReverbWebSocket';
import { ConnectionIndicator } from '@/components/WebSocket/ConnectionIndicator';
import { ImageModal } from '@/components/ui/ImageModal';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/Components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    ArrowLeft,
    MessageSquare,
    Send,
    Upload,
    X,
    FileText,
    Package,
    Calendar,
    User,
    Clock,
    CheckCircle,
    AlertTriangle,
    Download,
    XCircle,
    ChevronDown,
    ChevronUp,
    Info,
    ImageIcon,
    PaperclipIcon
} from 'lucide-react';

interface DisputeMessage {
    id: string;
    auteur_type: string;
    auteur_nom: string;
    message: string;
    type_message: string;
    pieces_jointes: Array<{
        nom_original: string;
        chemin: string;
        taille: number;
    }>;
    created_at: string;
}

interface Dispute {
    id: string;
    numero_litige: string;
    type_litige: string;
    sujet: string;
    description: string;
    solution_souhaitee?: string;
    statut: string;
    statut_formate: string;
    statut_color: string;
    priorite_formate: string;
    priorite_color: string;
    date_ouverture: string;
    date_limite_reponse?: string;
    montant_conteste?: number;
    montant_rembourse: number;
    commande_principale: {
        numero_commande: string;
        montant_total_ttc: number;
    };
    messages: DisputeMessage[];
    escrow_transaction?: {
        statut: string;
        montant_total: number;
    };
}

interface Props {
    dispute: Dispute;
    canAddMessage: boolean;
    canClose: boolean;
}

export default function DisputeShow({ dispute, canAddMessage, canClose }: Props) {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [showCloseConfirm, setShowCloseConfirm] = useState(false);
    const [messages, setMessages] = useState<DisputeMessage[]>(dispute.messages);
    const [showDisputeInfo, setShowDisputeInfo] = useState(false);
    const [imageModal, setImageModal] = useState<{
        isOpen: boolean;
        imageUrl: string;
        imageName: string;
        imageSize?: number;
        downloadUrl?: string;
    }>({
        isOpen: false,
        imageUrl: '',
        imageName: '',
        imageSize: 0,
        downloadUrl: ''
    });
    const { props } = usePage();
    const successMessage = (props as any).flash?.success;

    // Hook WebSocket pour écouter les nouveaux messages de litige
    const { } = useReverbWebSocket({
        disputeId: dispute.id,
        onMessage: (data) => {
            console.log('🔥 [CLIENT] Message WebSocket reçu:', data);

            // Vérifier la structure des données
            if (!data.message) {
                console.error('❌ [CLIENT] Pas de propriété message dans les données:', data);
                return;
            }

            // Ajouter le nouveau message à la liste
            const newMsg: DisputeMessage = {
                id: data.message.id,
                message: data.message.message,
                auteur_type: data.message.auteur_type,
                auteur_nom: data.message.auteur_nom,
                type_message: data.message.type_message,
                pieces_jointes: data.message.pieces_jointes || [],
                created_at: data.message.created_at,
            };

            console.log('✅ [CLIENT] Nouveau message créé:', newMsg);
            setMessages(prev => {
                const updated = [...prev, newMsg];
                console.log('📝 [CLIENT] Messages mis à jour:', updated);
                return updated;
            });

            // Auto-scroll vers le bas
            setTimeout(() => {
                document.getElementById('messages-end')?.scrollIntoView({ behavior: 'smooth' });
            }, 100);
        },
        onNotification: (data) => {
            console.log('🔔 [CLIENT] Notification litige reçue:', data);
        },
    });

    const { data, setData, post, processing, errors, reset } = useForm({
        message: '',
        pieces_jointes: [] as File[]
    });

    const { post: closeDispute, processing: closingDispute } = useForm();

    const handleClose = () => {
        closeDispute(route('client.disputes.close', dispute.id), {
            onSuccess: () => {
                setShowCloseConfirm(false);
            }
        });
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        const validFiles = files.filter(file => {
            const isValidType = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'].includes(file.type);
            const isValidSize = file.size <= 5120 * 1024; // 5MB
            return isValidType && isValidSize;
        });

        if (selectedFiles.length + validFiles.length <= 3) {
            const newFiles = [...selectedFiles, ...validFiles];
            setSelectedFiles(newFiles);
            setData('pieces_jointes', newFiles);
        }
    };

    const removeFile = (index: number) => {
        const newFiles = selectedFiles.filter((_, i) => i !== index);
        setSelectedFiles(newFiles);
        setData('pieces_jointes', newFiles);
    };

    // Fonctions pour le modal d'images
    const openImageModal = (attachment: any, message: any, attachmentIndex: number) => {
        const isImage = attachment.nom_original.match(/\.(jpg|jpeg|png|gif|webp)$/i);
        const imageUrl = isImage ? `/storage/${attachment.chemin}` : '';
        const downloadUrl = route('client.disputes.download', {
            messageId: message.id,
            attachmentIndex: attachmentIndex
        });

        setImageModal({
            isOpen: true,
            imageUrl,
            imageName: attachment.nom_original,
            imageSize: attachment.taille,
            downloadUrl
        });
    };

    const closeImageModal = () => {
        setImageModal({
            isOpen: false,
            imageUrl: '',
            imageName: '',
            imageSize: 0,
            downloadUrl: ''
        });
    };

    const handleSubmitMessage = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('client.disputes.add-message', dispute.id), {
            forceFormData: true,
            onSuccess: () => {
                console.log('✅ [CLIENT] Message envoyé avec succès');
                reset();
                setSelectedFiles([]);

                // Le message sera automatiquement ajouté via WebSocket
                // Pas besoin de recharger la page
            }
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatAmount = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    };

    const getAuthorIcon = (type: string) => {
        switch (type) {
            case 'client':
                return <User className="h-4 w-4" />;
            case 'admin':
                return <CheckCircle className="h-4 w-4" />;
            case 'system':
                return <Clock className="h-4 w-4" />;
            default:
                return <MessageSquare className="h-4 w-4" />;
        }
    };



    return (
        <ClientDashboardLayout>
            <Head title={`Litige ${dispute.numero_litige}`} />

            {/* Layout responsive optimisé */}
            <div className="h-screen flex flex-col">
                {/* Message de succès */}
                {successMessage && (
                    <Alert className="mx-4 mt-4">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            {successMessage}
                        </AlertDescription>
                    </Alert>
                )}

                {/* Header compact */}
                <div className="flex-shrink-0 p-4 border-b bg-white dark:bg-gray-800">
                    <div className="flex items-center gap-4">
                        <Link href={route('client.disputes.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Retour
                            </Button>
                        </Link>

                        {/* Info compacte mobile / normale desktop */}
                        <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                                <h1 className="text-lg md:text-2xl font-bold text-gray-900 dark:text-white">
                                    {dispute.numero_litige}
                                </h1>
                                <Badge variant={dispute.statut_color as any} className="text-xs status-badge">
                                    {dispute.statut_formate}
                                </Badge>
                                <Badge variant={dispute.priorite_color as any} className="text-xs status-badge">
                                    {dispute.priorite_formate}
                                </Badge>

                                {/* Bouton info mobile */}
                                <button
                                    onClick={() => setShowDisputeInfo(!showDisputeInfo)}
                                    className="md:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                    {showDisputeInfo ? <ChevronUp className="h-4 w-4" /> : <Info className="h-4 w-4" />}
                                </button>
                            </div>

                            <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 truncate">
                                {dispute.sujet}
                            </p>

                            {/* Info mobile déroulante */}
                            {showDisputeInfo && (
                                <div className="md:hidden mt-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Commande:</span>
                                        <span className="font-medium">{dispute.commande_principale.numero_commande}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Date:</span>
                                        <span>{new Date(dispute.date_ouverture).toLocaleDateString('fr-FR')}</span>
                                    </div>
                                    {dispute.montant_conteste && (
                                        <div className="flex justify-between">
                                            <span className="text-gray-500">Montant:</span>
                                            <span className="font-medium">{formatAmount(dispute.montant_conteste)}</span>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>

                        {canClose && (
                            <Button
                                variant="outline"
                                onClick={() => setShowCloseConfirm(true)}
                                className="text-red-600 hover:text-red-700 hidden md:flex"
                                size="sm"
                            >
                                <XCircle className="h-4 w-4 mr-2" />
                                Fermer
                            </Button>
                        )}
                    </div>
                </div>

                {/* Layout principal : Chat + Sidebar */}
                <div className="flex-1 flex gap-4 p-4 min-h-0">
                    {/* Zone de chat principale */}
                    <div className="flex-1 flex flex-col min-w-0">
                        <Card className="flex flex-col h-full">
                            <CardHeader className="flex-shrink-0 border-b">
                                <CardTitle className="flex items-center gap-2">
                                    <MessageSquare className="h-5 w-5" />
                                    Échanges ({messages.length})
                                    <ConnectionIndicator size="sm" className="connection-pulse" />
                                </CardTitle>
                            </CardHeader>

                            {/* Zone des messages avec scroll optimisé */}
                            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                                {messages.length === 0 ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="text-center">
                                            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                                            <p className="text-gray-500">Aucun message pour le moment</p>
                                            <p className="text-sm text-gray-400 mt-1">Commencez la conversation avec l'équipe support</p>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        {messages.map((message) => (
                                            <div
                                                key={message.id}
                                                className={`flex ${message.auteur_type === 'client' ? 'justify-end' : 'justify-start'} chat-bubble new-message`}
                                            >
                                                <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md ${
                                                    message.auteur_type === 'client'
                                                        ? 'bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 text-white'
                                                        : message.auteur_type === 'admin'
                                                        ? 'bg-green-100 dark:bg-green-900/20 text-green-900 dark:text-green-100 border border-green-200 dark:border-green-800'
                                                        : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700'
                                                }`}>

                                                    {message.auteur_type !== 'client' && (
                                                        <div className="flex items-center gap-2 mb-2">
                                                            {getAuthorIcon(message.auteur_type)}
                                                            <span className="text-sm font-medium">{message.auteur_nom}</span>
                                                            {message.type_message !== 'message' && (
                                                                <Badge variant="outline" className="text-xs">
                                                                    {message.type_message}
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    )}

                                                    <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                                        {message.message}
                                                    </p>

                                                    {message.pieces_jointes.length > 0 && (
                                                        <div className="mt-3 space-y-2">
                                                            {message.pieces_jointes.map((file, index) => {
                                                                const isImage = file.nom_original.match(/\.(jpg|jpeg|png|gif|webp)$/i);
                                                                return (
                                                                    <div key={index} className="flex items-center gap-2 p-2  bg-blue-900/20 rounded text-xs cursor-pointer ">
                                                                        {isImage ? (
                                                                            <ImageIcon className="h-3 w-3 text-blue-400" />
                                                                        ) : (
                                                                            <FileText className="h-3 w-3" />
                                                                        )}
                                                                        <button
                                                                            onClick={() => openImageModal(file, message, index)}
                                                                            className="truncate flex-1 text-left hover:underline cursor-pointer"
                                                                        >
                                                                            {file.nom_original}
                                                                        </button>
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            className="h-6 w-6 p-0 hover:bg-black/20 curson-pointer"
                                                                            onClick={() => openImageModal(file, message, index)}
                                                                        >
                                                                            <Download className="h-3 w-3" />
                                                                        </Button>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    )}

                                                    <div className={`text-xs mt-2 ${
                                                        message.auteur_type === 'client' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                                                    }`}>
                                                        {formatDate(message.created_at)}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}

                                        {/* Auto-scroll anchor */}
                                        <div id="messages-end" />
                                    </>
                                )}
                            </div>

                            {/* Zone de saisie attractive */}
                            {canAddMessage && (
                                <div className="flex-shrink-0 border-t bg-white dark:bg-gray-800 p-4">
                                    <form onSubmit={handleSubmitMessage} className="space-y-3">
                                        {/* Fichiers sélectionnés avec design amélioré */}
                                        {selectedFiles.length > 0 && (
                                            <div className="flex flex-wrap gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                                <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300 mb-2 w-full">
                                                    <PaperclipIcon className="h-4 w-4" />
                                                    <span className="font-medium">Fichiers joints ({selectedFiles.length}/3)</span>
                                                </div>
                                                {selectedFiles.map((file, index) => (
                                                    <div key={index} className="flex items-center gap-2 bg-white dark:bg-gray-800 rounded-lg px-3 py-2 border shadow-sm file-attachment">
                                                        <ImageIcon className="h-4 w-4 text-blue-500" />
                                                        <span className="text-sm truncate max-w-32">{file.name}</span>
                                                        <span className="text-xs text-gray-500">
                                                            {(file.size / 1024).toFixed(0)}KB
                                                        </span>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeFile(index)}
                                                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-colors"
                                                        >
                                                            <X className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        <div className="flex gap-3">
                                            <div className="flex-1">
                                                <Textarea
                                                    value={data.message}
                                                    onChange={(e) => setData('message', e.target.value)}
                                                    placeholder="Tapez votre message..."
                                                    rows={3}
                                                    maxLength={2000}
                                                    className="resize-none border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 chat-input"
                                                />
                                                <div className="flex justify-between items-center mt-1">
                                                    <span className="text-xs text-gray-500">
                                                        {data.message.length}/2000 caractères
                                                    </span>
                                                    {errors.message && (
                                                        <span className="text-xs text-red-600">{errors.message}</span>
                                                    )}
                                                </div>
                                            </div>

                                            <div className="flex flex-col gap-2">
                                                {/* Zone d'upload attractive */}
                                                <input
                                                    type="file"
                                                    multiple
                                                    accept=".jpg,.jpeg,.png,.pdf"
                                                    onChange={handleFileUpload}
                                                    className="hidden"
                                                    id="message-file-upload"
                                                />
                                                <Label htmlFor="message-file-upload" className="cursor-pointer">
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        className="h-10 w-10 p-0 border-dashed border-2 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 upload-zone"
                                                        disabled={selectedFiles.length >= 3}
                                                    >
                                                        <Upload className="h-4 w-4" />
                                                    </Button>
                                                </Label>

                                                <Button
                                                    type="submit"
                                                    disabled={processing || !data.message.trim()}
                                                    size="sm"
                                                    className="h-10 w-10 p-0"
                                                >
                                                    {processing ? (
                                                        <Clock className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <Send className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </Card>
                    </div>

                    {/* Sidebar informations (desktop uniquement) */}
                    <div className="hidden md:block w-80 flex-shrink-0">
                        <Card className="h-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-lg">
                                    <Info className="h-5 w-5" />
                                    Informations du litige
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Commande</Label>
                                        <div className="flex items-center gap-2 mt-1">
                                            <Package className="h-4 w-4 text-gray-400" />
                                            <span className="font-medium">{dispute.commande_principale.numero_commande}</span>
                                        </div>
                                        <span className="text-sm text-gray-500">
                                            {formatAmount(dispute.commande_principale.montant_total_ttc)}
                                        </span>
                                    </div>

                                    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
                                        <Label className="text-sm font-medium text-gray-500">Date d'ouverture</Label>
                                        <div className="flex items-center gap-2 mt-1">
                                            <Calendar className="h-4 w-4 text-gray-400" />
                                            <span>{formatDate(dispute.date_ouverture)}</span>
                                        </div>
                                    </div>

                                    {dispute.montant_conteste && (
                                        <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                                            <Label className="text-sm font-medium text-red-700 dark:text-red-300">Montant contesté</Label>
                                            <p className="mt-1 font-bold text-red-900 dark:text-red-100">{formatAmount(dispute.montant_conteste)}</p>
                                        </div>
                                    )}

                                    {dispute.montant_rembourse > 0 && (
                                        <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                            <Label className="text-sm font-medium text-green-700 dark:text-green-300">Montant remboursé</Label>
                                            <p className="mt-1 font-bold text-green-900 dark:text-green-100">{formatAmount(dispute.montant_rembourse)}</p>
                                        </div>
                                    )}
                                </div>

                                <div className="border-t pt-4">
                                    <Label className="text-sm font-medium text-gray-500">Description</Label>
                                    <p className="mt-2 text-sm text-gray-900 dark:text-white leading-relaxed">{dispute.description}</p>
                                </div>

                                {dispute.solution_souhaitee && (
                                    <div className="border-t pt-4">
                                        <Label className="text-sm font-medium text-gray-500">Solution souhaitée</Label>
                                        <p className="mt-2 text-sm text-gray-900 dark:text-white leading-relaxed">{dispute.solution_souhaitee}</p>
                                    </div>
                                )}

                                {dispute.escrow_transaction && (
                                    <Alert className="mt-4">
                                        <AlertTriangle className="h-4 w-4" />
                                        <AlertDescription className="text-sm">
                                            Les fonds de cette commande ({formatAmount(dispute.escrow_transaction.montant_total)})
                                            sont retenus en sécurité en attendant la résolution.
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {canClose && (
                                    <div className="border-t pt-4">
                                        <Button
                                            variant="outline"
                                            onClick={() => setShowCloseConfirm(true)}
                                            className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                                        >
                                            <XCircle className="h-4 w-4 mr-2" />
                                            Fermer le litige
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>



                {/* Confirmation de fermeture */}
                {showCloseConfirm && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <div className="space-y-3">
                                <p>Êtes-vous sûr de vouloir fermer ce litige ? Cette action est irréversible.</p>
                                <div className="flex gap-2">
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={handleClose}
                                        disabled={closingDispute}
                                    >
                                        {closingDispute ? 'Fermeture...' : 'Confirmer la fermeture'}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setShowCloseConfirm(false)}
                                    >
                                        Annuler
                                    </Button>
                                </div>
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Modal d'images */}
                <ImageModal
                    isOpen={imageModal.isOpen}
                    onClose={closeImageModal}
                    imageUrl={imageModal.imageUrl}
                    imageName={imageModal.imageName}
                    imageSize={imageModal.imageSize}
                    downloadUrl={imageModal.downloadUrl}
                />
            </div>
        </ClientDashboardLayout>
    );
}
