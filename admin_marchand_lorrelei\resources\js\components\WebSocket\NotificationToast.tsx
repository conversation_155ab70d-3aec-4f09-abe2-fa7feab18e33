import React, { useState, useEffect } from 'react';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface Notification {
    id: string;
    title: string;
    message: string;
    type: 'message' | 'dispute' | 'info' | 'warning' | 'error';
    timestamp: Date;
    read: boolean;
}

interface NotificationToastProps {
    maxNotifications?: number;
    autoHideDelay?: number;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const NotificationToast: React.FC<NotificationToastProps> = ({
    maxNotifications = 5,
    autoHideDelay = 5000,
    position = 'top-right',
}) => {
    const [notifications, setNotifications] = useState<Notification[]>([]);

    // Hook WebSocket pour les notifications admin
    const { } = useReverbWebSocket({
        onMessage: (data) => {
            // Créer une notification pour les nouveaux messages de litige
            addNotification({
                id: `dispute-msg-${data.message.id}`,
                title: 'Nouveau message de litige',
                message: `${data.message.auteur_nom}: ${data.message.message.substring(0, 50)}${data.message.message.length > 50 ? '...' : ''}`,
                type: 'dispute',
                timestamp: new Date(),
                read: false,
            });
        },
        onNotification: (data) => {
            // Ajouter les notifications générales
            addNotification({
                id: `notif-${Date.now()}`,
                title: data.title || 'Notification',
                message: data.message || '',
                type: data.type || 'info',
                timestamp: new Date(),
                read: false,
            });
        },
    });

    // Ajouter une nouvelle notification
    const addNotification = (notification: Notification) => {
        setNotifications(prev => {
            const newNotifications = [notification, ...prev];
            // Limiter le nombre de notifications
            return newNotifications.slice(0, maxNotifications);
        });

        // Auto-hide après le délai spécifié
        if (autoHideDelay > 0) {
            setTimeout(() => {
                removeNotification(notification.id);
            }, autoHideDelay);
        }
    };

    // Supprimer une notification
    const removeNotification = (id: string) => {
        setNotifications(prev => prev.filter(notif => notif.id !== id));
    };

    // Marquer comme lu
    const markAsRead = (id: string) => {
        setNotifications(prev =>
            prev.map(notif =>
                notif.id === id ? { ...notif, read: true } : notif
            )
        );
    };

    // Obtenir les classes de position
    const getPositionClasses = () => {
        switch (position) {
            case 'top-right':
                return 'top-4 right-4';
            case 'top-left':
                return 'top-4 left-4';
            case 'bottom-right':
                return 'bottom-4 right-4';
            case 'bottom-left':
                return 'bottom-4 left-4';
            default:
                return 'top-4 right-4';
        }
    };

    // Obtenir les classes de couleur selon le type
    const getTypeClasses = (type: string) => {
        switch (type) {
            case 'message':
                return 'bg-blue-500 border-blue-600';
            case 'dispute':
                return 'bg-red-500 border-red-600';
            case 'warning':
                return 'bg-yellow-500 border-yellow-600';
            case 'error':
                return 'bg-red-600 border-red-700';
            case 'info':
            default:
                return 'bg-gray-500 border-gray-600';
        }
    };

    // Obtenir l'icône selon le type
    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'message':
                return '💬';
            case 'dispute':
                return '⚠️';
            case 'warning':
                return '⚠️';
            case 'error':
                return '❌';
            case 'info':
            default:
                return 'ℹ️';
        }
    };

    // Formater le timestamp
    const formatTime = (timestamp: Date) => {
        return timestamp.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    if (notifications.length === 0) {
        return null;
    }

    return (
        <div className={`fixed ${getPositionClasses()} z-50 space-y-2 max-w-sm w-full`}>
            {notifications.map((notification) => (
                <div
                    key={notification.id}
                    className={`
                        transform transition-all duration-300 ease-in-out
                        ${notification.read ? 'opacity-75 scale-95' : 'opacity-100 scale-100'}
                        bg-white dark:bg-gray-800 
                        border-l-4 ${getTypeClasses(notification.type)}
                        rounded-lg shadow-lg 
                        p-4 cursor-pointer
                        hover:shadow-xl hover:scale-105
                    `}
                    onClick={() => markAsRead(notification.id)}
                >
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 text-lg">
                            {getTypeIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {notification.title}
                                </h4>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        removeNotification(notification.id);
                                    }}
                                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                    ×
                                </button>
                            </div>
                            
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                                {notification.message}
                            </p>
                            
                            <div className="flex items-center justify-between mt-2">
                                <span className="text-xs text-gray-500 dark:text-gray-500">
                                    {formatTime(notification.timestamp)}
                                </span>
                                
                                {!notification.read && (
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            ))}
            
            {/* Bouton pour effacer toutes les notifications */}
            {notifications.length > 1 && (
                <div className="text-center">
                    <button
                        onClick={() => setNotifications([])}
                        className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 underline"
                    >
                        Effacer tout ({notifications.length})
                    </button>
                </div>
            )}
        </div>
    );
};
