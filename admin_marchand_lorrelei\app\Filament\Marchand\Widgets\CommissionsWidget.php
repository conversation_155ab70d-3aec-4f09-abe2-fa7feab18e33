<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\Commission;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class CommissionsWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 5;

    protected static ?string $heading = 'Commissions récentes';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Commission::query()
                    ->where('marchand_id', auth()->user()->marchands->first()->id ?? null)
                    ->latest('created_at')
                    ->limit(15)
            )
            ->columns([
                Tables\Columns\TextColumn::make('reference_commission')
                    ->label('Référence')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Référence copiée!')
                    ->copyMessageDuration(1500),
                
                Tables\Columns\BadgeColumn::make('type_commission')
                    ->label('Type')
                    ->colors([
                        'primary' => 'vente',
                        'warning' => 'abonnement',
                        'info' => 'listing',
                        'success' => 'transaction',
                        'gray' => 'service',
                        'danger' => 'penalite',
                        'success' => 'bonus',
                    ])
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'vente' => 'Vente',
                        'abonnement' => 'Abonnement',
                        'listing' => 'Listing',
                        'transaction' => 'Transaction',
                        'service' => 'Service',
                        'penalite' => 'Pénalité',
                        'bonus' => 'Bonus',
                        'ajustement' => 'Ajustement',
                        default => ucfirst($state)
                    }),
                
                Tables\Columns\TextColumn::make('montant_base')
                    ->label('Base')
                    ->money('FCFA')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('taux_commission')
                    ->label('Taux')
                    ->suffix('%')
                    ->numeric(decimalPlaces: 2)
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('montant_commission')
                    ->label('Commission')
                    ->money('FCFA')
                    ->sortable()
                    ->weight('bold')
                    ->color('primary'),
                
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'gray' => 'calculee',
                        'warning' => 'confirmee',
                        'success' => 'collectee',
                        'info' => 'ajustee',
                        'danger' => 'annulee',
                    ])
                    ->icons([
                        'heroicon-m-calculator' => 'calculee',
                        'heroicon-m-check' => 'confirmee',
                        'heroicon-m-banknotes' => 'collectee',
                        'heroicon-m-pencil' => 'ajustee',
                        'heroicon-m-x-circle' => 'annulee',
                    ])
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'calculee' => 'Calculée',
                        'confirmee' => 'Confirmée',
                        'collectee' => 'Collectée',
                        'ajustee' => 'Ajustée',
                        'annulee' => 'Annulée',
                        default => ucfirst($state)
                    }),
                
                Tables\Columns\TextColumn::make('periode_facturation')
                    ->label('Période')
                    ->date('m/Y')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('date_calcul')
                    ->label('Calculée le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('date_collecte')
                    ->label('Collectée le')
                    ->dateTime('d/m/Y H:i')
                    ->placeholder('En attente')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('incluse_dans_versement')
                    ->label('Versée')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type_commission')
                    ->label('Type')
                    ->options([
                        'vente' => 'Vente',
                        'abonnement' => 'Abonnement',
                        'listing' => 'Listing',
                        'transaction' => 'Transaction',
                        'service' => 'Service',
                        'penalite' => 'Pénalité',
                        'bonus' => 'Bonus',
                        'ajustement' => 'Ajustement',
                    ]),
                
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'calculee' => 'Calculée',
                        'confirmee' => 'Confirmée',
                        'collectee' => 'Collectée',
                        'ajustee' => 'Ajustée',
                        'annulee' => 'Annulée',
                    ]),
                
                Tables\Filters\TernaryFilter::make('incluse_dans_versement')
                    ->label('Incluse dans versement')
                    ->placeholder('Toutes')
                    ->trueLabel('Oui')
                    ->falseLabel('Non'),
                
                Tables\Filters\Filter::make('periode')
                    ->form([
                        \Filament\Forms\Components\Select::make('mois')
                            ->label('Mois')
                            ->options([
                                1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
                                5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
                                9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                            ]),
                        \Filament\Forms\Components\Select::make('annee')
                            ->label('Année')
                            ->options(array_combine(
                                range(date('Y'), date('Y') - 5),
                                range(date('Y'), date('Y') - 5)
                            )),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['mois'],
                                fn (Builder $query, $mois): Builder => $query->where('mois_facturation', $mois),
                            )
                            ->when(
                                $data['annee'],
                                fn (Builder $query, $annee): Builder => $query->where('annee_facturation', $annee),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\Action::make('details')
                    ->label('Détails')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->modalHeading(fn (Commission $record): string => "Commission {$record->reference_commission}")
                    ->modalContent(fn (Commission $record): \Illuminate\Contracts\View\View => view('filament.modals.commission-details', ['commission' => $record]))
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Fermer'),
                
                Tables\Actions\Action::make('download_invoice')
                    ->label('Facture')
                    ->icon('heroicon-m-document-text')
                    ->color('success')
                    ->visible(fn (Commission $record): bool => $record->facture_generee)
                    ->url(fn (Commission $record): string => route('marchand.commissions.invoice', $record))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('export')
                        ->label('Exporter')
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function ($records) {
                            // Logique d'export
                            return response()->download(
                                storage_path('app/exports/commissions.csv')
                            );
                        }),
                ]),
            ])
            ->emptyStateHeading('Aucune commission')
            ->emptyStateDescription('Vos commissions apparaîtront ici une fois que vous aurez des ventes.')
            ->emptyStateIcon('heroicon-o-calculator')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([15, 25, 50]);
    }
}
