<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\StatsOverview;
use App\Filament\Widgets\TopMarchandsWidget;
use App\Filament\Widgets\GlobalOrdersWidget;
use App\Filament\Widgets\PayoutsManagementWidget;
use App\Filament\Widgets\DisputeManagementWidget;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    // protected static ?string $title = 'Dashboard Admin';

    protected function getHeaderWidgets(): array
    {
        return [
            StatsOverview::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            TopMarchandsWidget::class,
            DisputeManagementWidget::class, // Corrigé - redirige vers dashboard
            PayoutsManagementWidget::class,
            GlobalOrdersWidget::class, // ✅ Corrigé - utilise DashboardStatsService
        ];
    }
}
