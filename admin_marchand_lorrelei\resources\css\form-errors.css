/* Styles pour les champs avec erreurs */
.field-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500 focus:ring-2 ;
    animation: shake 0.5s ease-in-out;
}

.field-error:focus {
    @apply shadow-red-500/25;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Animation de secousse pour attirer l'attention */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Style pour les messages d'erreur */
.error-message {
    @apply text-red-500 text-sm mt-1 flex items-center gap-1;
    animation: slideDown 0.3s ease-out;
}

.error-message::before {
    content: "⚠";
    @apply text-red-400;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Style pour les Select avec erreurs */
.select-error [data-radix-select-trigger] {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500 focus:ring-2 ;
}

/* Style pour les Textarea avec erreurs */
.textarea-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500 focus:ring-2 ;
}

/* Style pour les Checkbox avec erreurs */
.checkbox-error {
    @apply border-red-500 focus:ring-red-500;
}

/* Amélioration de la visibilité en mode sombre */
.dark .field-error {
    @apply border-red-400 focus:border-red-400 focus:ring-red-400;
}

.dark .error-message {
    @apply text-red-400;
}

.dark .field-error:focus {
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.1);
}
