import { useState, useEffect } from 'react';
import { usePage } from '@inertiajs/react';
import axios from 'axios';

interface NotificationData {
    conversations_non_lues: number;
    disputes_non_lues: number;
    total_non_lus: number;
}

interface SharedData {
    auth: {
        user?: any;
    };
}

/**
 * Hook pour gérer les notifications avec vérification d'authentification
 * Ne fait les appels API que si l'utilisateur est authentifié
 */
export function useAuthenticatedNotifications() {
    const { props } = usePage<SharedData>();
    const isAuthenticated = !!props.auth?.user;
    
    const [notifications, setNotifications] = useState<NotificationData>({
        conversations_non_lues: 0,
        disputes_non_lues: 0,
        total_non_lus: 0
    });
    
    const [loading, setLoading] = useState(false);

    const fetchNotifications = async () => {
        // Ne pas faire l'appel API si l'utilisateur n'est pas authentifié
        if (!isAuthenticated) {
            return notifications;
        }

        try {
            setLoading(true);
            const response = await axios.get('/api/notifications/unread-count');
            setNotifications(response.data);
            return response.data;
        } catch (error: any) {
            // Si erreur 401 (non authentifié), ne pas logger comme erreur
            if (error.response?.status === 401) {
                console.log('Utilisateur non authentifié, pas de notifications à charger');
            } else {
                console.error('Erreur lors du chargement des notifications:', error);
            }
            // En cas d'erreur, garder les valeurs par défaut
            setNotifications({
                conversations_non_lues: 0,
                disputes_non_lues: 0,
                total_non_lus: 0
            });
            return notifications;
        } finally {
            setLoading(false);
        }
    };

    const markAllAsRead = async () => {
        // Ne pas faire l'appel API si l'utilisateur n'est pas authentifié
        if (!isAuthenticated) {
            return false;
        }

        try {
            await axios.post('/api/notifications/mark-all-read');
            setNotifications({
                conversations_non_lues: 0,
                disputes_non_lues: 0,
                total_non_lus: 0
            });
            return true;
        } catch (error: any) {
            console.error('Erreur lors du marquage comme lu:', error);
            return false;
        }
    };

    useEffect(() => {
        // Seulement charger les notifications si l'utilisateur est authentifié
        if (isAuthenticated) {
            fetchNotifications();
            
            // Actualiser toutes les 30 secondes si authentifié
            const interval = setInterval(fetchNotifications, 30000);
            return () => clearInterval(interval);
        }
    }, [isAuthenticated]);

    return {
        notifications,
        loading,
        isAuthenticated,
        fetchNotifications,
        markAllAsRead
    };
}
