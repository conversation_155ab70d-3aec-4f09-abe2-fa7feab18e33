<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ClientMarchandConversation;
use App\Models\Dispute;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Obtenir le nombre de messages non lus pour le client connecté
     */
    public function getUnreadCount()
    {
        $clientId = Auth::user()->client->id ?? null;
        
        if (!$clientId) {
            return response()->json([
                'conversations_non_lues' => 0,
                'disputes_non_lues' => 0,
                'total_non_lus' => 0
            ]);
        }

        // Compter les messages non lus dans les conversations
        $conversationsNonLues = ClientMarchandConversation::where('client_id', $clientId)
            ->where('messages_non_lus_client', '>', 0)
            ->sum('messages_non_lus_client');

        // Compter les messages non lus dans les litiges
        $disputesNonLues = Dispute::where('client_id', $clientId)
            ->whereHas('messages', function ($query) {
                $query->where('lu_par_client', false)
                      ->where('auteur_type', '!=', 'client');
            })
            ->withCount(['messages as messages_non_lus' => function ($query) {
                $query->where('lu_par_client', false)
                      ->where('auteur_type', '!=', 'client');
            }])
            ->get()
            ->sum('messages_non_lus');

        $totalNonLus = $conversationsNonLues + $disputesNonLues;

        return response()->json([
            'conversations_non_lues' => $conversationsNonLues,
            'disputes_non_lues' => $disputesNonLues,
            'total_non_lus' => $totalNonLus
        ]);
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    public function markAllAsRead()
    {
        $clientId = Auth::user()->client->id ?? null;
        
        if (!$clientId) {
            return response()->json(['success' => false, 'message' => 'Client non trouvé']);
        }

        // Marquer les conversations comme lues
        ClientMarchandConversation::where('client_id', $clientId)
            ->update(['messages_non_lus_client' => 0]);

        // Marquer les messages de litiges comme lus
        Dispute::where('client_id', $clientId)
            ->with('messages')
            ->get()
            ->each(function ($dispute) {
                $dispute->messages()
                    ->where('lu_par_client', false)
                    ->where('auteur_type', '!=', 'client')
                    ->update(['lu_par_client' => true]);
            });

        return response()->json(['success' => true]);
    }
}
