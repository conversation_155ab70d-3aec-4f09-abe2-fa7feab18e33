<?php

namespace App\Events;

use App\Models\MerchantValidation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MerchantRejected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public MerchantValidation $validation;
    public string $rejectionReason;

    /**
     * Create a new event instance.
     */
    public function __construct(MerchantValidation $validation, string $rejectionReason)
    {
        $this->validation = $validation;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
