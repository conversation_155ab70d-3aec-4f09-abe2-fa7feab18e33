<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\DisputeService;
use App\Models\Dispute;
use App\Models\DisputeMessage;
use App\Models\CommandePrincipale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Inertia\Inertia;
use Inertia\Response;

class DisputeController extends Controller
{
    protected DisputeService $disputeService;

    public function __construct(DisputeService $disputeService)
    {
        $this->disputeService = $disputeService;
    }

    /**
     * Affiche la liste des litiges du client
     */
    public function index(Request $request): Response
    {
        $client = Auth::user()->client;

        if (!$client) {
            abort(403, 'Accès non autorisé');
        }

        // Récupérer les litiges du client avec pagination
        $disputes = Dispute::where('client_id', $client->id)
            ->with(['commandePrincipale', 'sousCommande.marchand', 'messages' => function($query) {
                $query->publics()->latest()->limit(1);
            }])
            ->when($request->get('statut'), function($query, $statut) {
                return $query->where('statut', $statut);
            })
            ->when($request->get('type'), function($query, $type) {
                return $query->where('type_litige', $type);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Statistiques pour le client
        $stats = [
            'total' => Dispute::where('client_id', $client->id)->count(),
            'ouverts' => Dispute::where('client_id', $client->id)->ouverts()->count(),
            'resolus' => Dispute::where('client_id', $client->id)->where('statut', 'resolu')->count(),
            'en_retard' => Dispute::where('client_id', $client->id)->enRetard()->count(),
        ];

        return Inertia::render('Client/Disputes/Index', [
            'disputes' => [
                'data' => $disputes->items(),
                'meta' => [
                    'current_page' => $disputes->currentPage(),
                    'last_page' => $disputes->lastPage(),
                    'per_page' => $disputes->perPage(),
                    'total' => $disputes->total(),
                    'from' => $disputes->firstItem(),
                    'to' => $disputes->lastItem(),
                ],
                'links' => $disputes->links()->elements
            ],
            'stats' => $stats,
            'filters' => $request->only(['statut', 'type']),
            'typesLitige' => $this->getTypesLitige(),
            'statutsLitige' => $this->getStatutsLitige()
        ]);
    }

    /**
     * Affiche le formulaire de création d'un litige
     */
    public function create(Request $request): Response
    {
        $client = Auth::user()->client;

        if (!$client) {
            abort(403, 'Accès non autorisé');
        }

        // Récupérer les commandes éligibles pour litige
        $commandes = CommandePrincipale::where('client_id', $client->id)
            ->whereIn('statut_global', ['PayementConfirme', 'EnPreparation', 'PartielLivré', 'TotalementLivré'])
            ->with(['sousCommandes.marchand'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Si une commande est spécifiée dans l'URL
        $commandeSelectionnee = null;
        if ($request->get('commande_id')) {
            $commandeSelectionnee = $commandes->firstWhere('id', $request->get('commande_id'));
        }

        return Inertia::render('Client/Disputes/Create', [
            'commandes' => $commandes,
            'commandeSelectionnee' => $commandeSelectionnee,
            'typesLitige' => $this->getTypesLitige(),
            'maxFileSize' => config('app.max_file_upload_size', 5120) // 5MB par défaut
        ]);
    }

    /**
     * Crée un nouveau litige
     */
    public function store(Request $request)
    {
        $client = Auth::user()->client;

        if (!$client) {
            return back()->withErrors(['error' => 'Accès non autorisé']);
        }

        try {
            // Validation des données
            $validated = $request->validate([
                'commande_principale_id' => 'required|exists:commandes_principales,id',
                'sous_commande_id' => 'nullable|exists:sous_commandes_vendeur,id',
                'type_litige' => 'required|in:non_livraison,produit_defectueux,produit_different,livraison_partielle,retard_livraison,frais_supplementaires,service_client,autre',
                'sujet' => 'required|string|max:255',
                'description' => 'required|string|max:2000',
                'solution_souhaitee' => 'nullable|string|max:1000',
                'montant_conteste' => 'nullable|numeric|min:0',
                'pieces_jointes' => 'nullable|array|max:5',
                'pieces_jointes.*' => 'file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120' // 5MB max
            ]);

            // Vérifier que la commande appartient au client
            $commande = CommandePrincipale::where('id', $validated['commande_principale_id'])
                ->where('client_id', $client->id)
                ->first();

            if (!$commande) {
                return back()->withErrors(['commande_principale_id' => 'Commande non trouvée']);
            }

            // Traiter les pièces jointes
            $piecesJointes = [];
            if ($request->hasFile('pieces_jointes')) {
                foreach ($request->file('pieces_jointes') as $file) {
                    $path = $file->store('disputes/attachments', 'public');
                    $piecesJointes[] = [
                        'nom_original' => $file->getClientOriginalName(),
                        'nom_fichier' => basename($path),
                        'chemin' => $path,
                        'taille' => $file->getSize(),
                        'type_mime' => $file->getMimeType(),
                        'uploaded_at' => now()->toISOString()
                    ];
                }
            }

            // Préparer les données pour le service
            $disputeData = array_merge($validated, [
                'pieces_jointes' => $piecesJointes,
                'source' => 'web_client'
            ]);

            // Créer le litige via le service
            $result = $this->disputeService->creerLitige($disputeData);

            if ($result['success']) {
                return redirect()
                    ->route('client.disputes.show', $result['dispute']->id)
                    ->with('success', 'Votre litige a été créé avec succès. Numéro : ' . $result['numero_litige']);
            } else {
                return back()
                    ->withErrors(['error' => 'Erreur lors de la création du litige'])
                    ->withInput();
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            Log::error('Erreur création litige client', [
                'client_id' => $client->id,
                'error' => $e->getMessage(),
                'request_data' => $request->except(['pieces_jointes'])
            ]);

            return back()
                ->withErrors(['error' => 'Une erreur est survenue lors de la création du litige'])
                ->withInput();
        }
    }

    /**
     * Affiche les détails d'un litige
     */
    public function show(string $id): Response
    {
        $client = Auth::user()->client;

        if (!$client) {
            abort(403, 'Accès non autorisé');
        }

        $dispute = Dispute::where('id', $id)
            ->where('client_id', $client->id)
            ->with([
                'commandePrincipale.sousCommandes.marchand',
                'sousCommande.marchand',
                'messages' => function($query) {
                    $query->publics()->orderBy('created_at', 'asc');
                },
                'escrowTransaction'
            ])
            ->firstOrFail();

        // Marquer les messages comme lus par le client
        $dispute->messages()->nonLusPar('client')->update(['lu_par_client' => true]);

        return Inertia::render('Client/Disputes/Show', [
            'dispute' => $dispute,
            'canAddMessage' => in_array($dispute->statut, ['ouvert', 'en_cours', 'attente_client']),
            'canClose' => in_array($dispute->statut, ['ouvert', 'en_cours', 'attente_client'])
        ]);
    }

    /**
     * Ajoute un message à un litige
     */
    public function addMessage(Request $request, string $id)
    {
        $client = Auth::user()->client;

        if (!$client) {
            return back()->withErrors(['error' => 'Accès non autorisé']);
        }

        try {
            // Validation
            $validated = $request->validate([
                'message' => 'required|string|max:2000',
                'pieces_jointes' => 'nullable|array|max:3',
                'pieces_jointes.*' => 'file|mimes:jpg,jpeg,png,pdf|max:5120'
            ]);

            // Vérifier que le litige appartient au client
            $dispute = Dispute::where('id', $id)
                ->where('client_id', $client->id)
                ->firstOrFail();

            // Vérifier que le client peut ajouter un message
            if (!in_array($dispute->statut, ['ouvert', 'en_cours', 'attente_client'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vous ne pouvez plus ajouter de message à ce litige'
                ], 403);
            }

            // Traiter les pièces jointes
            $piecesJointes = [];
            if ($request->hasFile('pieces_jointes')) {
                foreach ($request->file('pieces_jointes') as $file) {
                    $path = $file->store('disputes/messages', 'public');
                    $piecesJointes[] = [
                        'nom_original' => $file->getClientOriginalName(),
                        'nom_fichier' => basename($path),
                        'chemin' => $path,
                        'taille' => $file->getSize(),
                        'type_mime' => $file->getMimeType(),
                        'uploaded_at' => now()->toISOString()
                    ];
                }
            }

            // Préparer les données du message
            $messageData = [
                'auteur_type' => 'client',
                'auteur_id' => $client->id,
                'auteur_nom' => $client->nom . ' ' . $client->prenom,
                'message' => $validated['message'],
                'type_message' => 'message',
                'pieces_jointes' => $piecesJointes,
                'interne' => false
            ];

            // Ajouter le message via le service
            $result = $this->disputeService->ajouterMessage($dispute->id, $messageData);

            if ($result['success']) {
                return back()->with('success', 'Votre message a été ajouté avec succès');
            } else {
                return back()->withErrors(['error' => 'Erreur lors de l\'ajout du message']);
            }

        } catch (\Exception $e) {
            Log::error('Erreur ajout message litige client', [
                'client_id' => $client->id,
                'dispute_id' => $id,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Une erreur est survenue lors de l\'ajout du message']);
        }
    }

    /**
     * Ferme un litige (côté client)
     */
    public function close(string $id)
    {
        $client = Auth::user()->client;

        if (!$client) {
            return back()->withErrors(['error' => 'Accès non autorisé']);
        }

        try {
            // Vérifier que le litige appartient au client
            $dispute = Dispute::where('id', $id)
                ->where('client_id', $client->id)
                ->firstOrFail();

            // Vérifier que le client peut fermer le litige
            if (!in_array($dispute->statut, ['ouvert', 'en_cours', 'attente_client'])) {
                return back()->withErrors(['error' => 'Ce litige ne peut plus être fermé']);
            }

            // Fermer le litige via le service
            $result = $this->disputeService->changerStatut(
                $dispute->id,
                'ferme_client',
                null,
                'Fermé par le client'
            );

            if ($result['success']) {
                return back()->with('success', 'Le litige a été fermé avec succès');
            } else {
                return back()->withErrors(['error' => 'Erreur lors de la fermeture du litige']);
            }

        } catch (\Exception $e) {
            Log::error('Erreur fermeture litige client', [
                'client_id' => $client->id,
                'dispute_id' => $id,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Une erreur est survenue lors de la fermeture du litige']);
        }
    }

    /**
     * Obtient les types de litige disponibles
     */
    private function getTypesLitige(): array
    {
        return [
            'non_livraison' => 'Commande non livrée',
            'produit_defectueux' => 'Produit défectueux ou endommagé',
            'produit_different' => 'Produit différent de la description',
            'livraison_partielle' => 'Livraison incomplète',
            'retard_livraison' => 'Retard de livraison',
            'frais_supplementaires' => 'Frais supplémentaires non prévus',
            'service_client' => 'Problème de service client',
            'autre' => 'Autre motif'
        ];
    }

    /**
     * Obtient les statuts de litige pour les filtres
     */
    private function getStatutsLitige(): array
    {
        return [
            'ouvert' => 'Ouvert',
            'en_cours' => 'En cours de traitement',
            'attente_client' => 'En attente de votre réponse',
            'attente_marchand' => 'En attente du marchand',
            'resolu' => 'Résolu',
            'ferme_client' => 'Fermé par vous',
            'ferme_admin' => 'Fermé par l\'administration'
        ];
    }

    /**
     * Télécharge une pièce jointe d'un message de litige
     */
    public function downloadAttachment(string $messageId, string $attachmentIndex): HttpResponse
    {
        $client = Auth::user()->client;

        if (!$client) {
            abort(403, 'Accès non autorisé');
        }

        try {
            // Récupérer le message et vérifier qu'il appartient à un litige du client
            $message = DisputeMessage::where('id', $messageId)
                ->whereHas('dispute', function($query) use ($client) {
                    $query->where('client_id', $client->id);
                })
                ->where('visible_client', true)
                ->firstOrFail();

            // Vérifier que la pièce jointe existe
            if (!$message->pieces_jointes || !isset($message->pieces_jointes[$attachmentIndex])) {
                abort(404, 'Pièce jointe non trouvée');
            }

            $attachment = $message->pieces_jointes[$attachmentIndex];

            // Vérifier que le fichier existe sur le disque
            $filePath = $attachment['chemin'];
            if (!Storage::disk('public')->exists($filePath)) {
                abort(404, 'Fichier non trouvé sur le serveur');
            }

            // Log du téléchargement
            Log::info('Téléchargement pièce jointe litige', [
                'client_id' => $client->id,
                'message_id' => $messageId,
                'dispute_id' => $message->dispute_id,
                'attachment' => $attachment['nom_original'],
                'file_path' => $filePath
            ]);

            // Retourner le fichier
            return response()->download(
                Storage::disk('public')->path($filePath),
                $attachment['nom_original'],
                [
                    'Content-Type' => $attachment['type_mime'] ?? 'application/octet-stream'
                ]
            );

        } catch (\Exception $e) {
            Log::error('Erreur téléchargement pièce jointe litige', [
                'client_id' => $client->id,
                'message_id' => $messageId,
                'attachment_index' => $attachmentIndex,
                'error' => $e->getMessage()
            ]);

            abort(500, 'Erreur lors du téléchargement');
        }
    }
}
