<?php

namespace App\Filament\Marchand\Resources\MessageResource\Pages;

use App\Filament\Marchand\Resources\MessageResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMessage extends CreateRecord
{
    protected static string $resource = MessageResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ajouter automatiquement l'ID du marchand connecté
        $data['marchand_id'] = auth()->user()->marchand?->id;
        $data['date_creation'] = now();
        $data['date_derniere_activite'] = now();
        
        return $data;
    }
}
