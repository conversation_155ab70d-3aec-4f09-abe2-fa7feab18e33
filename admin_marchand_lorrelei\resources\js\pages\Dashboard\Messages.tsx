import React, { useState, useEffect, useRef } from 'react';
import { Head } from '@inertiajs/react';
import {
    ChatBubbleLeftRightIcon,
    UserIcon,
    PaperClipIcon,
    PaperAirplaneIcon,
    MagnifyingGlassIcon,
    XMarkIcon,
    CheckIcon,
    ClockIcon,
    ArrowPathIcon,
    Bars3Icon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import ThemeSelector from '@/components/ThemeSelector';
import AttachmentDisplay from '@/Components/AttachmentDisplay';
import ImageModal, { useImageModal } from '@/components/ImageModal';

interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
}

interface Client {
    id: number;
    user: User;
}

interface Conversation {
    id: number;
    sujet: string;
    statut: 'active' | 'fermee' | 'archivee';
    client: Client;
    date_creation: string;
    date_dernier_message: string;
    messages_non_lus_marchand: number;
    type_conversation: string;
}

interface Message {
    id: number;
    message: string;
    auteur_type: 'client' | 'marchand';
    auteur_nom: string;
    type_message: string;
    pieces_jointes?: string[];
    lu_par_client: boolean;
    lu_par_marchand: boolean;
    created_at: string;
}

interface ConversationDetail {
    conversation: Conversation;
    messages: Message[];
}

export default function Messages() {
    const [conversations, setConversations] = useState<Conversation[]>([]);
    const [selectedConversation, setSelectedConversation] = useState<ConversationDetail | null>(null);
    const [loading, setLoading] = useState(true);
    const [loadingMessages, setLoadingMessages] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [newMessage, setNewMessage] = useState('');
    const [messageType, setMessageType] = useState('message');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [sending, setSending] = useState(false);
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { isOpen, imageData, openModal, closeModal } = useImageModal();

    // Charger les conversations
    const loadConversations = async () => {
        try {
            const response = await axios.get('/api/messages', {
                params: { search: searchTerm, status: statusFilter }
            });
            setConversations(response.data.conversations);
        } catch (error) {
            console.error('Erreur lors du chargement des conversations:', error);
        } finally {
            setLoading(false);
        }
    };

    // Charger une conversation spécifique
    const loadConversation = async (id: number) => {
        setLoadingMessages(true);
        try {
            const response = await axios.get(`/api/messages/${id}`);
            setSelectedConversation(response.data);
            setSidebarOpen(false); // Fermer la sidebar sur mobile
        } catch (error) {
            console.error('Erreur lors du chargement de la conversation:', error);
        } finally {
            setLoadingMessages(false);
        }
    };

    // Envoyer un message
    const sendMessage = async () => {
        if (!newMessage.trim() || !selectedConversation || sending) return;

        setSending(true);
        try {
            const formData = new FormData();
            formData.append('message', newMessage);
            formData.append('type_message', messageType);
            
            attachments.forEach((file, index) => {
                formData.append(`pieces_jointes[${index}]`, file);
            });

            const response = await axios.post(
                `/api/messages/${selectedConversation.conversation.id}/messages`,
                formData,
                { headers: { 'Content-Type': 'multipart/form-data' } }
            );

            // Ajouter le nouveau message à la conversation
            setSelectedConversation(prev => prev ? {
                ...prev,
                messages: [...prev.messages, response.data.message]
            } : null);

            // Réinitialiser le formulaire
            setNewMessage('');
            setMessageType('message');
            setAttachments([]);
            
            // Recharger les conversations pour mettre à jour les compteurs
            loadConversations();
        } catch (error) {
            console.error('Erreur lors de l\'envoi du message:', error);
        } finally {
            setSending(false);
        }
    };

    // Fermer une conversation
    const closeConversation = async (id: number) => {
        try {
            await axios.patch(`/api/messages/${id}/close`);
            loadConversations();
            if (selectedConversation?.conversation.id === id) {
                setSelectedConversation(null);
            }
        } catch (error) {
            console.error('Erreur lors de la fermeture:', error);
        }
    };

    // Gérer les fichiers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setAttachments(prev => [...prev, ...files]);
    };

    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index));
    };

    // Auto-scroll vers le bas
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        loadConversations();
    }, [searchTerm, statusFilter]);

    useEffect(() => {
        if (selectedConversation) {
            scrollToBottom();
        }
    }, [selectedConversation?.messages]);

    // Formatage des dates
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 jours
            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'fermee': return 'bg-gray-100 text-gray-800';
            case 'archivee': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'active': return 'Active';
            case 'fermee': return 'Fermée';
            case 'archivee': return 'Archivée';
            default: return status;
        }
    };

    return (
        <>
            <Head title="Messages - Dashboard Marchand" />

            <div className="h-screen flex bg-gray-50 dark:bg-gray-900">
                {/* Overlay mobile */}
                {sidebarOpen && (
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                        onClick={() => setSidebarOpen(false)}
                    />
                )}

                {/* Sidebar des conversations */}
                <div className={`
                    ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                    lg:translate-x-0 fixed lg:relative z-50 lg:z-auto
                    w-80 lg:w-1/3 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
                    flex flex-col transition-transform duration-300 ease-in-out
                `}>
                    {/* Header */}
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <h1 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                                <ChatBubbleLeftRightIcon className="w-6 h-6 mr-2 text-blue-600 dark:text-blue-400" />
                                Messages
                            </h1>
                            <div className="flex items-center space-x-2">
                                <ThemeSelector className="hidden lg:block" />
                                <button
                                    onClick={() => setSidebarOpen(false)}
                                    className="lg:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                                >
                                    <XMarkIcon className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                        
                        {/* Recherche et filtres */}
                        <div className="mt-4 space-y-2">
                            <div className="relative">
                                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                                <input
                                    type="text"
                                    placeholder="Rechercher..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                             bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                             focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                             placeholder-gray-500 dark:placeholder-gray-400"
                                />
                            </div>

                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">Tous les statuts</option>
                                <option value="active">Active</option>
                                <option value="fermee">Fermée</option>
                                <option value="archivee">Archivée</option>
                            </select>
                        </div>
                    </div>

                    {/* Liste des conversations */}
                    <div className="flex-1 overflow-y-auto">
                        {loading ? (
                            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                                <ArrowPathIcon className="w-6 h-6 animate-spin mx-auto mb-2" />
                                Chargement...
                            </div>
                        ) : conversations.length === 0 ? (
                            <div className="p-4 text-center text-gray-500 dark:text-gray-400">Aucune conversation trouvée</div>
                        ) : (
                            conversations.map((conversation) => (
                                <div
                                    key={conversation.id}
                                    onClick={() => loadConversation(conversation.id)}
                                    className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer
                                              hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                                        selectedConversation?.conversation.id === conversation.id
                                            ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700'
                                            : ''
                                    }`}
                                >
                                    <div className="flex items-start space-x-3">
                                        {/* Avatar */}
                                        <div className="flex-shrink-0">
                                            {conversation.client.user.avatar ? (
                                                <img
                                                    src={conversation.client.user.avatar}
                                                    alt={conversation.client.user.name}
                                                    className="w-10 h-10 rounded-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                    <UserIcon className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Contenu */}
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                    {conversation.client.user.name}
                                                </p>
                                                <div className="flex items-center space-x-2">
                                                    {conversation.messages_non_lus_marchand > 0 && (
                                                        <span className="bg-blue-600 dark:bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                                            {conversation.messages_non_lus_marchand}
                                                        </span>
                                                    )}
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {formatDate(conversation.date_dernier_message)}
                                                    </span>
                                                </div>
                                            </div>

                                            <p className="text-sm text-gray-600 dark:text-gray-300 truncate mt-1">
                                                {conversation.sujet}
                                            </p>
                                            
                                            <div className="flex items-center justify-between mt-2">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(conversation.statut)}`}>
                                                    {getStatusLabel(conversation.statut)}
                                                </span>

                                                <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                                                    {conversation.type_conversation.replace('_', ' ')}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                {/* Zone de chat */}
                <div className="flex-1 flex flex-col relative">
                    {/* Bouton menu mobile */}
                    <button
                        onClick={() => setSidebarOpen(true)}
                        className="lg:hidden fixed top-4 left-4 z-30 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
                    >
                        <Bars3Icon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    </button>

                    {selectedConversation ? (
                        <>
                            {/* Header de la conversation */}
                            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4 pl-16 lg:pl-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        {selectedConversation.conversation.client.user.avatar ? (
                                            <img
                                                src={selectedConversation.conversation.client.user.avatar}
                                                alt={selectedConversation.conversation.client.user.name}
                                                className="w-10 h-10 rounded-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                <UserIcon className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                                            </div>
                                        )}

                                        <div>
                                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                {selectedConversation.conversation.client.user.name}
                                            </h2>
                                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                                {selectedConversation.conversation.sujet}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedConversation.conversation.statut)}`}>
                                            {getStatusLabel(selectedConversation.conversation.statut)}
                                        </span>
                                        
                                        {selectedConversation.conversation.statut === 'active' && (
                                            <button
                                                onClick={() => closeConversation(selectedConversation.conversation.id)}
                                                className="px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300
                                                         border border-red-300 dark:border-red-600 rounded-lg
                                                         hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                                            >
                                                Fermer
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Messages */}
                            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
                                {loadingMessages ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="text-center">
                                            <ArrowPathIcon className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                                            <p className="text-gray-500 dark:text-gray-400">Chargement des messages...</p>
                                        </div>
                                    </div>
                                ) : (
                                    selectedConversation.messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`flex ${message.auteur_type === 'marchand' ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm ${
                                            message.auteur_type === 'marchand'
                                                ? 'bg-blue-600 dark:bg-blue-500 text-white'
                                                : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'
                                        }`}>
                                            <p className="text-sm">{message.message}</p>
                                            
                                            <AttachmentDisplay
                                                attachments={message.pieces_jointes || []}
                                                messageId={message.id.toString()}
                                                messageType="conversation"
                                                onImageClick={openModal}
                                            />
                                            
                                            <div className={`text-xs mt-2 ${
                                                message.auteur_type === 'marchand'
                                                    ? 'text-blue-100 dark:text-blue-200'
                                                    : 'text-gray-500 dark:text-gray-400'
                                            }`}>
                                                <span className="font-medium">{message.auteur_nom}</span>
                                                <span className="mx-1">•</span>
                                                <span>{formatDate(message.created_at)}</span>

                                                {message.auteur_type === 'marchand' && (
                                                    <>
                                                        <span className="mx-1">•</span>
                                                        {message.lu_par_client ? (
                                                            <CheckIcon className="w-3 h-3 inline text-green-300 dark:text-green-400" />
                                                        ) : (
                                                            <ClockIcon className="w-3 h-3 inline text-blue-200 dark:text-blue-300" />
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    ))
                                )}
                                <div ref={messagesEndRef} />
                            </div>

                            {/* Formulaire de réponse */}
                            {selectedConversation.conversation.statut === 'active' && (
                                <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
                                    {/* Pièces jointes */}
                                    {attachments.length > 0 && (
                                        <div className="mb-3 flex flex-wrap gap-2">
                                            {attachments.map((file, index) => (
                                                <div key={index} className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-1">
                                                    <PaperClipIcon className="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" />
                                                    <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-[150px]">{file.name}</span>
                                                    <button
                                                        onClick={() => removeAttachment(index)}
                                                        className="ml-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                                    >
                                                        <XMarkIcon className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                        <select
                                            value={messageType}
                                            onChange={(e) => setMessageType(e.target.value)}
                                            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                                     bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                     focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        >
                                            <option value="message">Message</option>
                                            <option value="reponse_question">Réponse à une question</option>
                                            <option value="information_produit">Information produit</option>
                                            <option value="mise_a_jour_commande">Mise à jour commande</option>
                                            <option value="resolution_probleme">Résolution de problème</option>
                                        </select>

                                        <div className="flex-1 flex space-x-2">
                                            <input
                                                type="file"
                                                ref={fileInputRef}
                                                onChange={handleFileSelect}
                                                multiple
                                                accept="image/*,.pdf,.doc,.docx"
                                                className="hidden"
                                            />

                                            <button
                                                onClick={() => fileInputRef.current?.click()}
                                                className="px-3 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200
                                                         border border-gray-300 dark:border-gray-600 rounded-lg
                                                         hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                            >
                                                <PaperClipIcon className="w-5 h-5" />
                                            </button>

                                            <input
                                                type="text"
                                                value={newMessage}
                                                onChange={(e) => setNewMessage(e.target.value)}
                                                onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
                                                placeholder="Tapez votre message..."
                                                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                         focus:ring-2 focus:ring-blue-500 focus:border-transparent
                                                         placeholder-gray-500 dark:placeholder-gray-400"
                                            />

                                            <button
                                                onClick={sendMessage}
                                                disabled={!newMessage.trim() || sending}
                                                className="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg
                                                         hover:bg-blue-700 dark:hover:bg-blue-600
                                                         disabled:opacity-50 disabled:cursor-not-allowed
                                                         transition-colors flex items-center"
                                            >
                                                {sending ? (
                                                    <ArrowPathIcon className="w-5 h-5 animate-spin" />
                                                ) : (
                                                    <PaperAirplaneIcon className="w-5 h-5" />
                                                )}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900 pl-16 lg:pl-0">
                            <div className="text-center">
                                <ChatBubbleLeftRightIcon className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Sélectionnez une conversation</h3>
                                <p className="text-gray-500 dark:text-gray-400">Choisissez une conversation dans la liste pour commencer à discuter</p>
                                <button
                                    onClick={() => setSidebarOpen(true)}
                                    className="mt-4 lg:hidden px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Voir les conversations
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Modal d'image */}
            {imageData && (
                <ImageModal
                    isOpen={isOpen}
                    onClose={closeModal}
                    imageUrl={imageData.url}
                    imageName={imageData.name}
                    imageSize={imageData.size}
                />
            )}
        </>
    );
}
