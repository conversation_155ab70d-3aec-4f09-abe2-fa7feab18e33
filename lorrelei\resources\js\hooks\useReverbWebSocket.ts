import { useEffect, useRef, useState, useCallback } from 'react';
import ReverbWebSocketService from '../services/ReverbWebSocketService';

interface UseReverbWebSocketProps {
    conversationId?: string;
    disputeId?: string;
    userId?: string;
    onMessage?: (message: any) => void;
    onTyping?: (data: any) => void;
    onRead?: (data: any) => void;
    onStatusChange?: (data: any) => void;
    onNotification?: (data: any) => void;
}

export const useReverbWebSocket = ({
    conversationId,
    disputeId,
    userId,
    onMessage,
    onTyping,
    onRead,
    onStatusChange,
    onNotification,
}: UseReverbWebSocketProps) => {
    const [isConnected, setIsConnected] = useState(ReverbWebSocketService.connected);
    const [typingUsers, setTypingUsers] = useState<Array<{
        user_id: number;
        user_name: string;
        user_type: string;
    }>>([]);

    const typingTimeoutRef = useRef<Map<number, NodeJS.Timeout>>(new Map());
    const lastTypingRef = useRef<number>(0);

    // Gérer les changements de connexion
    useEffect(() => {
        const handleConnectionChange = (connected: boolean) => {
            setIsConnected(connected);
        };

        ReverbWebSocketService.onConnectionChange = handleConnectionChange;

        return () => {
            ReverbWebSocketService.onConnectionChange = undefined;
        };
    }, []);

    // S'abonner aux canaux selon les props
    useEffect(() => {
        if (conversationId) {
            ReverbWebSocketService.subscribeToConversation(conversationId, {
                onMessage: (data) => {
                    onMessage?.(data);
                },
                onTyping: (data) => {
                    handleTypingEvent(data);
                    onTyping?.(data);
                },
                onRead: onRead,
            });
        }

        if (disputeId) {
            ReverbWebSocketService.subscribeToDispute(disputeId, {
                onMessage: (data) => {
                    onMessage?.(data);
                },
                onStatusChange: onStatusChange,
                onTyping: (data) => {
                    handleTypingEvent(data);
                    onTyping?.(data);
                },
            });
        }

        if (userId) {
            ReverbWebSocketService.subscribeToUserNotifications(userId, {
                onNotification: onNotification,
            });
        }

        // Cleanup lors du démontage
        return () => {
            if (conversationId) {
                ReverbWebSocketService.unsubscribe(`conversation.${conversationId}`);
            }
            if (disputeId) {
                ReverbWebSocketService.unsubscribe(`dispute.${disputeId}`);
            }
            if (userId) {
                ReverbWebSocketService.unsubscribe(`user.${userId}`);
            }
        };
    }, [conversationId, disputeId, userId]);

    // Gérer les événements de frappe
    const handleTypingEvent = useCallback((data: any) => {
        const { user_id, user_name, user_type, is_typing } = data;

        if (is_typing) {
            // Ajouter l'utilisateur à la liste des personnes qui tapent
            setTypingUsers(prev => {
                const filtered = prev.filter(u => u.user_id !== user_id);
                return [...filtered, { user_id, user_name, user_type }];
            });

            // Supprimer automatiquement après 3 secondes
            const existingTimeout = typingTimeoutRef.current.get(user_id);
            if (existingTimeout) {
                clearTimeout(existingTimeout);
            }

            const timeout = setTimeout(() => {
                setTypingUsers(prev => prev.filter(u => u.user_id !== user_id));
                typingTimeoutRef.current.delete(user_id);
            }, 3000);

            typingTimeoutRef.current.set(user_id, timeout);
        } else {
            // Supprimer immédiatement l'utilisateur
            setTypingUsers(prev => prev.filter(u => u.user_id !== user_id));
            const timeout = typingTimeoutRef.current.get(user_id);
            if (timeout) {
                clearTimeout(timeout);
                typingTimeoutRef.current.delete(user_id);
            }
        }
    }, []);

    // Fonction pour envoyer l'état "en train d'écrire"
    const sendTyping = useCallback((isTyping: boolean = true) => {
        if (!conversationId) return;

        const now = Date.now();

        // Throttle les événements de frappe (max 1 par seconde)
        if (isTyping && now - lastTypingRef.current < 1000) {
            return;
        }

        lastTypingRef.current = now;
        ReverbWebSocketService.sendTyping(conversationId, isTyping);
    }, [conversationId]);

    // Fonction pour envoyer l'état "arrêt de frappe"
    const stopTyping = useCallback(() => {
        sendTyping(false);
    }, [sendTyping]);

    // Hook pour gérer automatiquement la frappe dans un input
    const useTypingInput = () => {
        const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
        const isTypingRef = useRef(false);

        const handleInputChange = useCallback((value: string) => {
            // Commencer à taper si ce n'est pas déjà le cas
            if (value.length > 0 && !isTypingRef.current) {
                isTypingRef.current = true;
                sendTyping(true);
            }

            // Réinitialiser le timeout d'arrêt
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }

            typingTimeoutRef.current = setTimeout(() => {
                if (isTypingRef.current) {
                    isTypingRef.current = false;
                    sendTyping(false);
                }
            }, 1000);
        }, []);

        const handleInputBlur = useCallback(() => {
            if (isTypingRef.current) {
                isTypingRef.current = false;
                sendTyping(false);
            }
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
        }, []);

        return {
            handleInputChange,
            handleInputBlur,
        };
    };

    // Demander la permission pour les notifications
    const requestNotificationPermission = useCallback(async () => {
        return await ReverbWebSocketService.requestNotificationPermission();
    }, []);

    // Cleanup des timeouts lors du démontage
    useEffect(() => {
        return () => {
            typingTimeoutRef.current.forEach(timeout => clearTimeout(timeout));
            typingTimeoutRef.current.clear();
        };
    }, []);

    return {
        // État
        isConnected,
        typingUsers,
        activeChannels: ReverbWebSocketService.activeChannels,

        // Fonctions
        sendTyping,
        stopTyping,
        useTypingInput,
        requestNotificationPermission,

        // Utilitaires
        formatTypingUsers: (users: typeof typingUsers) => {
            if (users.length === 0) return '';
            if (users.length === 1) return `${users[0].user_name} est en train d'écrire...`;
            if (users.length === 2) return `${users[0].user_name} et ${users[1].user_name} sont en train d'écrire...`;
            return `${users[0].user_name} et ${users.length - 1} autres sont en train d'écrire...`;
        },

        // Statistiques de connexion
        connectionStats: {
            connected: isConnected,
            activeChannels: ReverbWebSocketService.activeChannels.length,
        },
    };
};
